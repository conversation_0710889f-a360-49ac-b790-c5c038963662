import argparse
import os
import pickle
import numpy as np
import tensorflow as tf
from utils import (
    clear_memory, setup_gpu, setup_mixed_precision, load_phase_data,
    prepare_training_data, visualize_prediction, compare_fields,
    predict_with_original_resolution, generate_multi_step_predictions,
    GPUMonitor, TempFieldMonitor
)

# ========== 相场模型结构 ==========
def create_phase_model(input_shape, output_dim):
    with tf.device('/GPU:0'):
        field_input = tf.keras.layers.Input(shape=input_shape)
        # ResNet风格结构
        x = tf.keras.layers.Conv2D(32, (7, 7), strides=(2, 2), padding='same')(field_input)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Activation('relu')(x)
        x = tf.keras.layers.MaxPooling2D((3, 3), strides=(2, 2), padding='same')(x)
        # 残差块1
        shortcut = x
        x = tf.keras.layers.Conv2D(32, (3, 3), padding='same')(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Activation('relu')(x)
        x = tf.keras.layers.Conv2D(32, (3, 3), padding='same')(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.add([x, shortcut])
        x = tf.keras.layers.Activation('relu')(x)
        # 残差块2
        shortcut = tf.keras.layers.Conv2D(64, (1, 1), strides=(2, 2))(x)
        shortcut = tf.keras.layers.BatchNormalization()(shortcut)
        x = tf.keras.layers.Conv2D(64, (3, 3), strides=(2, 2), padding='same')(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Activation('relu')(x)
        x = tf.keras.layers.Conv2D(64, (3, 3), padding='same')(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.add([x, shortcut])
        x = tf.keras.layers.Activation('relu')(x)
        # 通道注意力
        se = tf.keras.layers.GlobalAveragePooling2D()(x)
        se = tf.keras.layers.Dense(64 // 4, activation='relu')(se)
        se = tf.keras.layers.Dense(64, activation='sigmoid')(se)
        x = tf.keras.layers.Multiply()([x, tf.keras.layers.Reshape((1, 1, 64))(se)])
        # 空间注意力
        spatial = tf.keras.layers.Conv2D(1, (7, 7), padding='same', activation='sigmoid')(x)
        x = tf.keras.layers.Multiply()([x, spatial])
        # 全局特征提取
        x = tf.keras.layers.GlobalAveragePooling2D()(x)
        # 全连接层
        x = tf.keras.layers.Dense(256, activation='relu')(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Dropout(0.3)(x)
        branch_output = tf.keras.layers.Dense(output_dim)(x)
        model = tf.keras.Model(inputs=field_input, outputs=branch_output)
    optimizer = tf.keras.optimizers.Adam(learning_rate=1e-4, clipnorm=1.0)
    if tf.config.list_physical_devices('GPU'):
        optimizer = tf.keras.mixed_precision.LossScaleOptimizer(optimizer)
    model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])
    return model

# ========== 参数解析 ==========
def parse_args():
    parser = argparse.ArgumentParser(description='相场训练参数')
    parser.add_argument('--phi_data_dir', type=str, default='E:/Freezedata/PhaseField_Data/', help='相场数据目录')
    parser.add_argument('--model_dir', type=str, default='./models', help='模型保存目录')
    parser.add_argument('--results_dir', type=str, default='./models/results', help='结果保存目录')
    parser.add_argument('--epochs', type=int, default=10, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=2, help='批次大小')
    parser.add_argument('--stride', type=int, default=10, help='数据加载步长')
    parser.add_argument('--max_samples', type=int, default=5000, help='最大加载样本数')
    parser.add_argument('--start_step', type=int, default=0, help='起始步数')
    parser.add_argument('--end_step', type=int, default=3050, help='结束步数')
    parser.add_argument('--downsample', type=int, default=4, help='空间降采样因子')
    parser.add_argument('--use_data_augmentation', action='store_true', default=True, help='是否使用数据增强')
    parser.add_argument('--load_model', action='store_true', help='是否加载已有模型')
    parser.add_argument('--save_model', action='store_true', help='是否保存模型')
    parser.add_argument('--generate_multi_step', action='store_false', help='是否生成多时间步预测图')
    parser.add_argument('--multi_step_start', type=int, default=0, help='多步预测起始步')
    parser.add_argument('--multi_step_end', type=int, default=3000, help='多步预测结束步')
    parser.add_argument('--multi_step_interval', type=int, default=500, help='多步预测间隔')

    # 新增监控和控制参数
    parser.add_argument('--enable_gpu_monitor', action='store_true', default=True, help='是否启用GPU监控')
    parser.add_argument('--enable_phase_monitor', action='store_true', default=True, help='是否启用相场训练监控')
    parser.add_argument('--use_early_stopping', action='store_true', default=False, help='是否使用早停机制')
    parser.add_argument('--patience', type=int, default=20, help='早停耐心值')
    parser.add_argument('--min_delta', type=float, default=1e-4, help='早停最小改善值')
    parser.add_argument('--enable_detailed_memory', action='store_true', default=False, help='是否启用详细内存监控')
    parser.add_argument('--enable_lr_scheduler', action='store_true', default=False, help='是否启用学习率调度器')
    parser.add_argument('--lr_reduce_factor', type=float, default=0.5, help='学习率衰减因子')
    parser.add_argument('--lr_reduce_patience', type=int, default=10, help='学习率衰减耐心值')
    return parser.parse_args()

# ========== 主流程 ==========
def main():
    args = parse_args()
    os.makedirs(args.model_dir, exist_ok=True)
    os.makedirs(args.results_dir, exist_ok=True)
    # 配置GPU
    gpu_available = setup_gpu()
    if gpu_available:
        setup_mixed_precision()
    # 加载数据
    print('加载相场数据...')
    phi_data = load_phase_data(
        args.phi_data_dir,
        start_step=args.start_step, end_step=args.end_step,
        stride=args.stride, max_samples=args.max_samples
    )
    clear_memory()
    # 训练数据准备
    print('准备训练数据...')
    X_phi, _, y_phi, _ = prepare_training_data(
        phi_data, downsample_factor=args.downsample, field_type='phase',
        use_data_augmentation=args.use_data_augmentation
    )
    del phi_data
    clear_memory()
    # 构建模型
    phi_model_path = os.path.join(args.model_dir, 'phi_model.keras')
    if args.load_model and os.path.exists(phi_model_path):
        print(f'加载已有模型: {phi_model_path}')
        model = tf.keras.models.load_model(phi_model_path)
        history = None
    else:
        print('开始训练相场模型...')
        model = create_phase_model(X_phi[0].shape, y_phi[0].shape[0])
        # 设置回调
        callbacks = []

        # 早停机制
        if args.use_early_stopping:
            early_stopping = tf.keras.callbacks.EarlyStopping(
                monitor='loss',
                patience=args.patience,
                min_delta=args.min_delta,
                verbose=1,
                restore_best_weights=True
            )
            callbacks.append(early_stopping)
            print(f"已启用早停机制: 耐心值={args.patience}")

        # GPU监控
        if args.enable_gpu_monitor:
            gpu_monitor = GPUMonitor(enabled=True)
            callbacks.append(gpu_monitor)
            print("已启用GPU监控")

        # 相场训练监控
        if args.enable_phase_monitor:
            phase_monitor = TempFieldMonitor(model_type='phase', enabled=True)
            callbacks.append(phase_monitor)
            print("已启用相场训练监控")

        # 学习率调度器
        if args.enable_lr_scheduler:
            lr_scheduler = tf.keras.callbacks.ReduceLROnPlateau(
                monitor='loss',
                factor=args.lr_reduce_factor,
                patience=args.lr_reduce_patience,
                verbose=1,
                min_delta=args.min_delta
            )
            callbacks.append(lr_scheduler)
            print(f"已启用学习率调度器")

        # 模型检查点
        if args.save_model:
            checkpoint_path = os.path.join(args.model_dir, 'phi_model_checkpoint.keras')
            checkpoint = tf.keras.callbacks.ModelCheckpoint(
                checkpoint_path,
                monitor='loss',
                save_best_only=True,
                verbose=1
            )
            callbacks.append(checkpoint)
            print(f"已启用模型检查点: {checkpoint_path}")

        # 训练模型
        print("\n开始训练相场模型...")
        history = model.fit(
            X_phi, y_phi,
            epochs=args.epochs,
            batch_size=args.batch_size,
            callbacks=callbacks,
            verbose=1
        )
        if args.save_model:
            print(f'保存模型到: {phi_model_path}')
            model.save(phi_model_path)
            with open(os.path.join(args.results_dir, 'phi_history.pkl'), 'wb') as f:
                pickle.dump(history.history, f)
    # 预测与可视化
    print('加载少量数据用于预测...')
    pred_phi_data = load_phase_data(
        args.phi_data_dir,
        start_step=max(0, args.end_step-10), end_step=args.end_step,
        stride=1, max_samples=10
    )
    phi_pred = predict_with_original_resolution(
        model, pred_phi_data[-1], downsample_factor=args.downsample
    )
    visualize_prediction(pred_phi_data[-1], phi_pred, 'phase field', args.phi_data_dir, reshape_pred=False, save_dir=args.results_dir)
    np.save(os.path.join(args.results_dir, 'phi_prediction.npy'), phi_pred)
    compare_fields(pred_phi_data[-1], phi_pred, '相场误差分析', args.results_dir)
    # 训练历史可视化
    if history is not None:
        try:
            import matplotlib.pyplot as plt
            plt.figure(figsize=(10, 6))
            plt.plot(history.history['loss'], label='Training Loss', linewidth=2)
            if 'mae' in history.history:
                plt.plot(history.history['mae'], label='Training MAE', linewidth=2)
            plt.title('Phase Field Training History', fontsize=14)
            plt.xlabel('Epoch', fontsize=12)
            plt.ylabel('Loss/MAE', fontsize=12)
            plt.legend(fontsize=10)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()

            history_path = os.path.join(args.results_dir, 'phase_training_history.png')
            plt.savefig(history_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"✅ 训练历史图已保存到: {history_path}")
        except Exception as e:
            print(f"❌ 保存训练历史图失败: {str(e)}")
    else:
        print("⚠️ 没有训练历史数据（可能是加载了已有模型）")
    # 多步预测
    if args.generate_multi_step:
        generate_multi_step_predictions(
            model, args.phi_data_dir, 'phase',
            args.multi_step_start, args.multi_step_end, args.multi_step_interval,
            downsample_factor=args.downsample, save_dir=args.results_dir
        )
    print('相场训练与预测完成！')

if __name__ == '__main__':
    main() 