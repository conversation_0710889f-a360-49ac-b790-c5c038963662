# DeepONet求解二维Cahn-Hilliard方程

本项目使用DeepONet模型求解二维Cahn-Hilliard方程，并可视化比较与传统数值方法的结果。

## Cahn-Hilliard方程

Cahn-Hilliard方程是一个描述相分离过程的四阶偏微分方程，其标准形式为：

$\frac{\partial u}{\partial t} = \nabla^2(-\varepsilon^2 \nabla^2 u + f(u))$

其中，$u$是相场变量，$\varepsilon$是界面宽度参数，$f(u) = u^3 - u$。

## DeepONet模型架构

DeepONet (Deep Operator Network) 是一种基于神经网络的算子学习方法，基于算子的通用逼近定理。它包括两个子网络：

1. **分支网络(Branch Network)**：处理输入函数的离散表示（传感器值）
2. **主干网络(Trunk Network)**：处理评估位置的坐标

DeepONet的输出由以下等式给出：

$G_\theta(v)(y) = \sum_{k=1}^p b_k(v) \cdot t_k(y) + b_0$

其中：
- $v$ 是输入函数（在我们的案例中是初始条件）
- $y$ 是评估位置和时间点
- $b_k(v)$ 是分支网络的输出
- $t_k(y)$ 是主干网络的输出
- $b_0$ 是偏置项

## 代码结构

本项目包含以下主要文件：

- `ch_solver.py`: CH方程数值求解器，用于生成训练和测试数据
- `deeponet_ch.py`: DeepONet模型实现和训练
- `visualization.py`: 结果可视化工具
- `main.py`: 主脚本，整合完整流程
- `requirements.txt`: 依赖项

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 生成数据集

生成用于训练DeepONet的数据集：

```bash
python main.py --mode data --n_samples 10 --n_steps 200
```

参数说明：
- `--n_samples`: 样本数量
- `--n_steps`: 每个样本的时间步数
- `--save_interval`: 保存间隔（默认为10）
- `--nx`, `--ny`: 网格点数（默认为64×64）
- `--epsilon`: 界面宽度参数（默认为0.05）
- `--dt`: 时间步长（默认为1e-4）

### 2. 训练模型

使用生成的数据集训练DeepONet模型：

```bash
python main.py --mode train --epochs 500 --batch_size 64
```

参数说明：
- `--epochs`: 训练轮数
- `--batch_size`: 批量大小
- `--data_path`: 数据集路径（默认为"ch_dataset.npz"）

### 3. 可视化结果

使用训练好的模型生成预测结果，并与真实解进行对比：

```bash
python main.py --mode visualize
```

参数说明：
- `--model_path`: 模型路径（默认为"models/deeponet_best.h5"）

### 4. 一键运行完整流程

```bash
python main.py --mode all
```

## 结果展示

运行可视化脚本后，将在`results/`目录下生成以下结果：

1. 2D热图对比：真实解、DeepONet预测和绝对误差
2. 1D截面对比：沿x轴和y轴的截面曲线对比
3. 误差统计表格
4. 误差随时间变化曲线

## 性能指标

项目使用以下指标评估DeepONet模型的性能：

- 平均绝对误差 (MAE)
- 均方根误差 (RMSE)
- 最大绝对误差 (Max Error)

## 参考资料

- Lu, L., Jin, P., Pang, G., Zhang, Z., & Karniadakis, G. E. (2021). Learning nonlinear operators via DeepONet based on the universal approximation theorem of operators. Nature Machine Intelligence, 3(3), 218-229.
- Cahn, J. W., & Hilliard, J. E. (1958). Free energy of a nonuniform system. I. Interfacial free energy. The Journal of chemical physics, 28(2), 258-267.
