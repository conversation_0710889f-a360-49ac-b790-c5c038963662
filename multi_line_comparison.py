#!/usr/bin/env python3
"""
多行/列温度场数据对比可视化工具
支持在同一图表中显示多个行或列的温度分布
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from datetime import datetime
import pandas as pd

# 设置matplotlib中文字体和美观样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-whitegrid')

def load_temp_data_single_step(temp_data_dir, step):
    """加载单个时间步的温度场数据"""
    temp_file = os.path.join(temp_data_dir, f"温度场第{step}步.csv")
    if not os.path.exists(temp_file):
        raise FileNotFoundError(f"文件不存在: {temp_file}")

    temp_data = pd.read_csv(temp_file, header=None, encoding='utf-8').values.astype(np.float32)
    return temp_data

def extract_line_data(data_array, line_type='row', line_index=400):
    """从2D数组中提取特定行或列的数据"""
    if line_type == 'row':
        if line_index >= data_array.shape[0]:
            raise ValueError(f"行索引 {line_index} 超出数组范围 {data_array.shape[0]}")
        return data_array[line_index, :]
    elif line_type == 'column':
        if line_index >= data_array.shape[1]:
            raise ValueError(f"列索引 {line_index} 超出数组范围 {data_array.shape[1]}")
        return data_array[:, line_index]
    else:
        raise ValueError("line_type 必须是 'row' 或 'column'")

def plot_multi_lines_gt_vs_pred(temp_data_dir, time_step, pred_data, line_indices, line_type='row', save_path=None):
    """
    在同一图表中对比Ground Truth和Prediction的多个行或列

    Args:
        temp_data_dir: 温度场数据目录
        time_step: 时间步
        pred_data: 预测数据数组
        line_indices: 要绘制的行或列索引列表
        line_type: 'row' 或 'column'
        save_path: 保存路径
    """
    try:
        # 加载指定时间步的Ground Truth数据
        gt_data = load_temp_data_single_step(temp_data_dir, time_step)
        print(f"✅ 成功加载第{time_step}步Ground Truth数据，形状: {gt_data.shape}")
        print(f"✅ 预测数据形状: {pred_data.shape}")

        # 创建美观的图形
        fig, ax = plt.subplots(figsize=(14, 8), facecolor='white')
        ax.set_facecolor('white')

        # 定义美观的颜色序列
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
                  '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']

        # 存储所有线的数据用于统计
        all_gt_lines = []
        all_pred_lines = []
        valid_indices = []

        # 绘制每条线的GT和Prediction对比
        for i, line_idx in enumerate(line_indices):
            try:
                gt_line = extract_line_data(gt_data, line_type, line_idx)
                pred_line = extract_line_data(pred_data, line_type, line_idx)
                x_coords = np.arange(len(gt_line))

                color = colors[i % len(colors)]
                line_label = f'Row {line_idx}' if line_type == 'row' else f'Col {line_idx}'

                # 绘制Ground Truth (实线)
                ax.plot(x_coords, gt_line, color=color, linewidth=2.5,
                       label=f'{line_label} GT', alpha=0.9, linestyle='-')

                # 绘制Prediction (虚线)
                ax.plot(x_coords, pred_line, color=color, linewidth=2.5,
                       label=f'{line_label} Pred', alpha=0.9, linestyle='--')

                all_gt_lines.append(gt_line)
                all_pred_lines.append(pred_line)
                valid_indices.append(line_idx)

            except ValueError as e:
                print(f"⚠️  跳过索引 {line_idx}: {str(e)}")
                continue

        if not all_gt_lines:
            print("❌ 没有有效的数据可以绘制")
            return None

        # 设置图形属性
        axis_label = 'X' if line_type == 'row' else 'Y'
        line_type_cn = '行' if line_type == 'row' else '列'

        ax.set_xlabel(f'{axis_label} Coordinate', fontsize=12, fontweight='bold')
        ax.set_ylabel('Temperature (K)', fontsize=12, fontweight='bold')

        title = f'GT vs Prediction - Multiple {line_type_cn.upper()}s (Step {time_step})'
        ax.set_title(title, fontsize=14, fontweight='bold', pad=20)

        # 美观的图例 - 分两列显示
        legend = ax.legend(fontsize=9, frameon=True, fancybox=True, shadow=True,
                          loc='upper right', bbox_to_anchor=(0.98, 0.98), ncol=2)
        legend.get_frame().set_facecolor('white')
        legend.get_frame().set_alpha(0.9)

        # 美观的网格
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.set_axisbelow(True)

        # 设置坐标轴样式
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(0.8)
        ax.spines['bottom'].set_linewidth(0.8)

        # 计算统计信息
        all_gt_data = np.concatenate(all_gt_lines)
        all_pred_data = np.concatenate(all_pred_lines)

        # 计算误差统计
        errors = []
        for gt_line, pred_line in zip(all_gt_lines, all_pred_lines):
            error = np.mean(np.abs(gt_line - pred_line))
            errors.append(error)

        mean_error = np.mean(errors)
        max_error = np.max(errors)

        # 添加统计信息框
        stats_text = f'Lines: {len(valid_indices)}\n' \
                    f'Time Step: {time_step}\n' \
                    f'Mean MAE: {mean_error:.3f}\n' \
                    f'Max MAE: {max_error:.3f}\n' \
                    f'GT Range: [{all_gt_data.min():.1f}, {all_gt_data.max():.1f}]\n' \
                    f'Pred Range: [{all_pred_data.min():.1f}, {all_pred_data.max():.1f}]'

        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
               verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5',
               facecolor='lightcyan', alpha=0.8, edgecolor='teal', linewidth=1))

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"✅ GT vs Prediction对比图已保存到: {save_path}")

        plt.show()
        return all_gt_lines, all_pred_lines

    except Exception as e:
        print(f"❌ 绘制GT vs Prediction对比图失败: {str(e)}")
        return None

def plot_multi_lines(data_array, line_indices, line_type='row', save_path=None, title_suffix=""):
    """
    在同一图表中绘制多个行或列的温度分布

    Args:
        data_array: 2D numpy数组
        line_indices: 要绘制的行或列索引列表
        line_type: 'row' 或 'column'
        save_path: 保存路径
        title_suffix: 标题后缀
    """
    # 创建美观的图形
    fig, ax = plt.subplots(figsize=(14, 8), facecolor='white')
    ax.set_facecolor('white')
    
    # 定义美观的颜色序列
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
              '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    
    # 存储所有线的数据用于统计
    all_lines_data = []
    
    # 绘制每条线
    for i, line_idx in enumerate(line_indices):
        try:
            line_data = extract_line_data(data_array, line_type, line_idx)
            x_coords = np.arange(len(line_data))
            
            color = colors[i % len(colors)]
            line_label = f'Row {line_idx}' if line_type == 'row' else f'Col {line_idx}'
            
            ax.plot(x_coords, line_data, color=color, linewidth=2.5, 
                   label=line_label, alpha=0.9)
            
            all_lines_data.append(line_data)
            
        except ValueError as e:
            print(f"⚠️  跳过索引 {line_idx}: {str(e)}")
            continue
    
    if not all_lines_data:
        print("❌ 没有有效的数据可以绘制")
        return
    
    # 设置图形属性
    axis_label = 'X' if line_type == 'row' else 'Y'
    line_type_cn = '行' if line_type == 'row' else '列'
    
    ax.set_xlabel(f'{axis_label} Coordinate', fontsize=12, fontweight='bold')
    ax.set_ylabel('Temperature (K)', fontsize=12, fontweight='bold')
    
    title = f'Temperature Profiles - Multiple {line_type_cn.upper()}s'
    if title_suffix:
        title += f' {title_suffix}'
    ax.set_title(title, fontsize=14, fontweight='bold', pad=20)
    
    # 美观的图例
    legend = ax.legend(fontsize=10, frameon=True, fancybox=True, shadow=True, 
                      loc='upper right', bbox_to_anchor=(0.98, 0.98))
    legend.get_frame().set_facecolor('white')
    legend.get_frame().set_alpha(0.9)
    
    # 美观的网格
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 设置坐标轴样式
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(0.8)
    ax.spines['bottom'].set_linewidth(0.8)
    
    # 计算统计信息
    all_data = np.concatenate(all_lines_data)
    global_min = np.min(all_data)
    global_max = np.max(all_data)
    global_mean = np.mean(all_data)
    global_std = np.std(all_data)
    
    # 添加统计信息框
    stats_text = f'Lines: {len(all_lines_data)}\n' \
                f'Global Range: [{global_min:.1f}, {global_max:.1f}]\n' \
                f'Global Mean: {global_mean:.3f}\n' \
                f'Global Std: {global_std:.3f}'
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
           verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5',
           facecolor='lightcyan', alpha=0.8, edgecolor='teal', linewidth=1))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"✅ 多线对比图已保存到: {save_path}")
    
    plt.show()
    return all_lines_data

def interactive_multi_line_console():
    """交互式多线对比控制台"""
    print("🎯 " + "="*60)
    print("    多行/列温度场对比可视化工具")
    print("="*64)

    # 选择对比模式
    print("\n📊 选择对比模式:")
    print("  1. Prediction Only (仅预测结果)")
    print("  2. Ground Truth Only (仅真实数据)")
    print("  3. GT vs Prediction (真实数据 vs 预测结果对比)")

    while True:
        mode_choice = input("请选择模式 (1/2/3, 默认3): ").strip()
        if mode_choice == '1':
            comparison_mode = 'prediction_only'
            break
        elif mode_choice == '2':
            comparison_mode = 'groundtruth_only'
            break
        elif mode_choice == '3' or mode_choice == '':
            comparison_mode = 'gt_vs_pred'
            break
        else:
            print("❌ 请输入 1、2 或 3")

    # 获取数据目录和结果目录
    if comparison_mode in ['groundtruth_only', 'gt_vs_pred']:
        # 需要Ground Truth数据
        default_data_dir = 'E:/Freezedata/TemperatureField_Data/'
        data_dir = input(f"📂 请输入温度场数据目录路径 (默认: {default_data_dir}): ").strip()
        if not data_dir:
            data_dir = default_data_dir

        if not os.path.exists(data_dir):
            print(f"❌ 目录不存在: {data_dir}")
            return None

        # 获取时间步
        while True:
            time_step_input = input("⏰ 请输入要分析的时间步 (默认: 2000): ").strip()
            if not time_step_input:
                time_step = 2000
                break
            try:
                time_step = int(time_step_input)
                if time_step < 0:
                    print("❌ 时间步必须为非负整数")
                    continue
                break
            except ValueError:
                print("❌ 请输入有效的整数")
    else:
        data_dir = None
        time_step = None

    if comparison_mode in ['prediction_only', 'gt_vs_pred']:
        # 需要预测结果数据
        default_results_dir = './models/results'
        results_dir = input(f"📂 请输入结果目录路径 (默认: {default_results_dir}): ").strip()
        if not results_dir:
            results_dir = default_results_dir

        if not os.path.exists(results_dir):
            print(f"❌ 目录不存在: {results_dir}")
            return None
    else:
        results_dir = os.path.join(os.path.dirname(data_dir), 'results') if data_dir else None
    
    # 获取线类型
    print("\n📊 选择对比类型:")
    print("  1. Row (行) - 多个水平截面对比")
    print("  2. Column (列) - 多个垂直截面对比")
    
    while True:
        choice = input("请选择 (1/2, 默认1): ").strip()
        if choice == '2':
            line_type = 'column'
            break
        elif choice == '1' or choice == '':
            line_type = 'row'
            break
        else:
            print("❌ 请输入 1 或 2")
    
    # 获取多个索引
    print(f"\n📍 请输入要对比的{'行' if line_type == 'row' else '列'}索引:")
    print("   支持格式:")
    print("   - 单个值: 400")
    print("   - 多个值: 300,400,500")
    print("   - 范围: 300-500-50 (从300到500，步长50)")
    
    while True:
        indices_input = input("请输入索引: ").strip()
        if not indices_input:
            print("❌ 请输入至少一个索引")
            continue
        
        try:
            line_indices = parse_indices(indices_input)
            if not line_indices:
                print("❌ 没有解析到有效的索引")
                continue
            print(f"✅ 解析到索引: {line_indices}")
            break
        except Exception as e:
            print(f"❌ 解析索引失败: {str(e)}")
    
    # 获取保存路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_save_name = f"multi_{line_type}_comparison_{timestamp}.png"
    default_save_path = os.path.join(results_dir, default_save_name)
    
    print(f"\n💾 图片保存设置:")
    save_path = input(f"请输入保存路径 (默认: {default_save_path}): ").strip()
    if not save_path:
        save_path = default_save_path
    
    # 确保保存目录存在
    save_dir = os.path.dirname(save_path)
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir, exist_ok=True)
        print(f"📁 已创建目录: {save_dir}")
    
    print("\n" + "="*64)
    print("📋 配置摘要:")
    print(f"   对比模式: {comparison_mode}")
    if comparison_mode in ['groundtruth_only', 'gt_vs_pred']:
        print(f"   数据目录: {data_dir}")
        print(f"   时间步: {time_step}")
    if comparison_mode in ['prediction_only', 'gt_vs_pred']:
        print(f"   结果目录: {results_dir}")
    print(f"   对比类型: {line_type}")
    print(f"   索引列表: {line_indices}")
    print(f"   保存路径: {save_path}")
    print("="*64)

    return {
        'comparison_mode': comparison_mode,
        'data_dir': data_dir,
        'time_step': time_step,
        'results_dir': results_dir,
        'line_type': line_type,
        'line_indices': line_indices,
        'save_path': save_path
    }

def parse_indices(indices_str):
    """解析索引字符串"""
    indices = []
    
    # 处理逗号分隔的多个值或范围
    parts = indices_str.split(',')
    
    for part in parts:
        part = part.strip()
        
        if '-' in part and not part.startswith('-'):
            # 处理范围格式 start-end-step
            range_parts = part.split('-')
            if len(range_parts) == 2:
                # start-end 格式，默认步长为1
                start, end = int(range_parts[0]), int(range_parts[1])
                indices.extend(range(start, end + 1))
            elif len(range_parts) == 3:
                # start-end-step 格式
                start, end, step = int(range_parts[0]), int(range_parts[1]), int(range_parts[2])
                indices.extend(range(start, end + 1, step))
        else:
            # 单个值
            indices.append(int(part))
    
    return sorted(list(set(indices)))  # 去重并排序

def main():
    parser = argparse.ArgumentParser(description='多行/列温度场对比可视化')
    parser.add_argument('--interactive', '-i', action='store_true',
                       help='启用交互式控制台')
    parser.add_argument('--comparison_mode', type=str, choices=['prediction_only', 'groundtruth_only', 'gt_vs_pred'], default='gt_vs_pred',
                       help='对比模式: prediction_only, groundtruth_only, 或 gt_vs_pred')
    parser.add_argument('--data_dir', type=str, default='E:/Freezedata/TemperatureField_Data/',
                       help='温度场数据目录路径')
    parser.add_argument('--time_step', type=int, default=2000,
                       help='时间步')
    parser.add_argument('--results_dir', type=str, default='./models/results',
                       help='结果目录路径')
    parser.add_argument('--line_type', type=str, choices=['row', 'column'], default='row',
                       help='对比类型：row(行) 或 column(列)')
    parser.add_argument('--line_indices', type=str, default='300,400,500',
                       help='行或列索引，支持逗号分隔或范围格式')
    parser.add_argument('--save_path', type=str, default='./models/results/1-temp/temp_profile.png',
                       help='图片保存路径')
    
    args = parser.parse_args()
    
    # 如果启用交互模式
    if args.interactive:
        config = interactive_multi_line_console()
        if config is None:
            return

        args.comparison_mode = config['comparison_mode']
        args.data_dir = config['data_dir']
        args.time_step = config['time_step']
        args.results_dir = config['results_dir']
        args.line_type = config['line_type']
        args.line_indices = config['line_indices']
        args.save_path = config['save_path']
    else:
        # 解析命令行参数中的索引
        args.line_indices = parse_indices(args.line_indices)

    # 设置保存路径
    if args.save_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if args.comparison_mode == 'groundtruth_only':
            results_dir = os.path.join(os.path.dirname(args.data_dir), 'results')
            os.makedirs(results_dir, exist_ok=True)
            args.save_path = os.path.join(results_dir,
                                        f'multi_{args.line_type}_gt_{args.time_step}_{timestamp}.png')
        elif args.comparison_mode == 'prediction_only':
            args.save_path = os.path.join(args.results_dir,
                                        f'multi_{args.line_type}_pred_{timestamp}.png')
        else:  # gt_vs_pred
            results_dir = os.path.join(os.path.dirname(args.data_dir), 'results')
            os.makedirs(results_dir, exist_ok=True)
            args.save_path = os.path.join(results_dir,
                                        f'multi_{args.line_type}_gt_vs_pred_{args.time_step}_{timestamp}.png')

    # 根据对比模式选择不同的处理流程
    if args.comparison_mode == 'groundtruth_only':
        # 仅绘制Ground Truth
        try:
            gt_data = load_temp_data_single_step(args.data_dir, args.time_step)
            all_lines_data = plot_multi_lines(gt_data, args.line_indices,
                                            args.line_type, args.save_path,
                                            title_suffix=f"(Ground Truth - Step {args.time_step})")

            if all_lines_data:
                print(f"\n📊 成功绘制 {len(all_lines_data)} 条线的Ground Truth对比图")

        except Exception as e:
            print(f"❌ 绘制Ground Truth对比图时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    elif args.comparison_mode == 'prediction_only':
        # 仅绘制预测结果
        temp_pred_path = os.path.join(args.results_dir, 'temp_prediction.npy')

        if not os.path.exists(temp_pred_path):
            print(f"❌ 未找到预测结果文件: {temp_pred_path}")
            print("请先运行温度场预测生成结果文件")
            return

        # 加载预测结果
        print(f"📂 加载预测结果: {temp_pred_path}")
        pred_data = np.load(temp_pred_path)
        print(f"预测数据形状: {pred_data.shape}")

        # 绘制多线对比图
        try:
            all_lines_data = plot_multi_lines(pred_data, args.line_indices,
                                            args.line_type, args.save_path,
                                            title_suffix="(Prediction)")

            print(f"\n📊 成功绘制 {len(all_lines_data)} 条线的预测对比图")

        except Exception as e:
            print(f"❌ 绘制预测对比图时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    else:  # gt_vs_pred
        # GT vs Prediction对比
        temp_pred_path = os.path.join(args.results_dir, 'temp_prediction.npy')

        if not os.path.exists(temp_pred_path):
            print(f"❌ 未找到预测结果文件: {temp_pred_path}")
            print("请先运行温度场预测生成结果文件")
            return

        # 加载预测结果
        print(f"📂 加载预测结果: {temp_pred_path}")
        pred_data = np.load(temp_pred_path)
        print(f"预测数据形状: {pred_data.shape}")

        # 绘制GT vs Prediction对比图
        try:
            result = plot_multi_lines_gt_vs_pred(
                args.data_dir, args.time_step, pred_data, args.line_indices,
                args.line_type, args.save_path
            )

            if result:
                all_gt_lines, all_pred_lines = result
                print(f"\n📊 成功绘制 {len(all_gt_lines)} 条线的GT vs Prediction对比图")

        except Exception as e:
            print(f"❌ 绘制GT vs Prediction对比图时出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
