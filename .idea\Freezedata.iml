<?xml version="1.0" encoding="UTF-8"?>
<module type="PYTHON_MODULE" version="4">
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/venv" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/venv/DLLs" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/venv/Lib" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/venv/Lib/site-packages" isTestSource="false" />
      <excludeFolder url="file://$MODULE_DIR$/22" />
      <excludeFolder url="file://$MODULE_DIR$/Freezedata" />
      <excludeFolder url="file://$MODULE_DIR$/venv" />
      <excludeFolder url="file://$MODULE_DIR$/deep" />
    </content>
    <orderEntry type="jdk" jdkName="tensorflow-gpu" jdkType="Python SDK" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
  <component name="PyDocumentationSettings">
    <option name="format" value="PLAIN" />
    <option name="myDocStringFormat" value="Plain" />
  </component>
</module>