import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt
import time
import os
from sklearn.model_selection import train_test_split
from tensorflow.keras.callbacks import ModelCheckpoint, EarlyStopping, ReduceLROnPlateau, TensorBoard

# 设置随机种子以保证可重复性
np.random.seed(42)
tf.random.set_seed(42)

class DeepONet:
    """
    DeepONet模型实现，基于TensorFlow 2.x
    
    分支网络处理输入函数，主干网络处理评估位置
    """
    
    def __init__(self, branch_dim, trunk_dim, branch_layers=[128, 128, 128], 
                 trunk_layers=[128, 128, 128], output_dim=1):
        """
        初始化DeepONet模型
        
        参数:
            branch_dim: 分支网络输入维度 (传感器数量)
            trunk_dim: 主干网络输入维度 (通常是坐标的维度，如2D+时间=3)
            branch_layers: 分支网络隐藏层节点数列表
            trunk_layers: 主干网络隐藏层节点数列表
            output_dim: 输出维度 (通常为1)
        """
        self.branch_dim = branch_dim
        self.trunk_dim = trunk_dim
        self.branch_layers = branch_layers
        self.trunk_layers = trunk_layers
        self.output_dim = output_dim
        
        # 创建DeepONet模型
        self.model = self._build_model()
    
    def _build_model(self):
        """构建改进的DeepONet模型"""
        # 分支网络
        branch_input = tf.keras.layers.Input(shape=(self.branch_dim,), name='branch_input')
        branch = branch_input
        
        # 添加批归一化层
        branch = tf.keras.layers.BatchNormalization()(branch)
        
        for i, width in enumerate(self.branch_layers):
            branch = tf.keras.layers.Dense(
                width, 
                activation='tanh',
                kernel_initializer='glorot_normal',
                name=f'branch_dense_{i}'
            )(branch)
            branch = tf.keras.layers.BatchNormalization()(branch)
        
        # 主干网络
        trunk_input = tf.keras.layers.Input(shape=(self.trunk_dim,), name='trunk_input')
        trunk = trunk_input
        
        # 添加批归一化层
        trunk = tf.keras.layers.BatchNormalization()(trunk)
        
        for i, width in enumerate(self.trunk_layers):
            trunk = tf.keras.layers.Dense(
                width, 
                activation='tanh',
                kernel_initializer='glorot_normal',
                name=f'trunk_dense_{i}'
            )(trunk)
            trunk = tf.keras.layers.BatchNormalization()(trunk)
        
        # 点积层
        dot_product = tf.keras.layers.Dot(axes=1)([branch, trunk])
        
        # 添加一个额外的全连接层
        hidden = tf.keras.layers.Dense(32, activation='tanh')(dot_product)
        output = tf.keras.layers.Dense(1, activation='linear')(hidden)
        
        return tf.keras.models.Model(inputs=[branch_input, trunk_input], outputs=output, name='DeepONet')
    
    def compile(self, learning_rate=1e-4):
        """编译模型"""
        optimizer = tf.keras.optimizers.Adam(
            learning_rate=learning_rate,
            clipnorm=1.0  # 添加梯度裁剪
        )
        
        # 均方误差损失
        self.model.compile(
            optimizer=optimizer,
            loss='mse',
            metrics=['mae']
        )
    
    def train(self, X_u, X_y, y, batch_size=64, epochs=100, validation_split=0.2,
              callbacks=None, verbose=1):
        """
        训练模型
        
        参数:
            X_u: 分支网络输入 (传感器值)
            X_y: 主干网络输入 (坐标和时间)
            y: 目标输出
            batch_size: 批量大小
            epochs: 训练轮数
            validation_split: 验证集比例
            callbacks: 回调函数
            verbose: 详细程度
            
        返回:
            训练历史
        """
        if callbacks is None:
            # 默认回调函数
            callbacks = [
                ModelCheckpoint('models/deeponet_best.h5', save_best_only=True, monitor='val_loss'),
                EarlyStopping(monitor='val_loss', patience=50, restore_best_weights=True),
                ReduceLROnPlateau(monitor='val_loss', factor=0.5, patience=20, min_lr=1e-6)
            ]
        
        # 确保保存目录存在
        os.makedirs('models', exist_ok=True)
        
        # 训练模型
        history = self.model.fit(
            [X_u, X_y], y,
            batch_size=batch_size,
            epochs=epochs,
            validation_split=validation_split,
            callbacks=callbacks,
            verbose=verbose
        )
        
        return history
    
    def evaluate(self, X_u, X_y, y, batch_size=64):
        """评估模型"""
        return self.model.evaluate([X_u, X_y], y, batch_size=batch_size)
    
    def predict(self, X_u, X_y, batch_size=64):
        """预测"""
        return self.model.predict([X_u, X_y], batch_size=batch_size)
    
    def save(self, filepath):
        """保存模型"""
        self.model.save(filepath)
    
    def load(self, filepath):
        """加载模型"""
        self.model = tf.keras.models.load_model(filepath)


def prepare_dataset(data_path):
    """准备数据集"""
    # 加载数据
    data = np.load(data_path)
    X_u, X_y, y = data['X_u'], data['X_y'], data['y']
    
    # 标准化输入数据
    X_u_mean = np.mean(X_u, axis=0)
    X_u_std = np.std(X_u, axis=0)
    X_u = (X_u - X_u_mean) / (X_u_std + 1e-8)
    
    # 标准化坐标和时间
    X_y_mean = np.mean(X_y, axis=0)
    X_y_std = np.std(X_y, axis=0)
    X_y = (X_y - X_y_mean) / (X_y_std + 1e-8)
    
    # 标准化输出数据
    y_mean = np.mean(y)
    y_std = np.std(y)
    y = (y - y_mean) / (y_std + 1e-8)
    
    # 划分训练集和测试集
    X_u_train, X_u_test, X_y_train, X_y_test, y_train, y_test = train_test_split(
        X_u, X_y, y, test_size=0.2, random_state=42
    )
    
    return (X_u_train, X_y_train, y_train), (X_u_test, X_y_test, y_test)


def train_model(data_path, epochs=100, batch_size=32):
    """训练DeepONet模型"""
    # 准备数据集
    (X_u_train, X_y_train, y_train), (X_u_test, X_y_test, y_test) = prepare_dataset(data_path)
    
    print("数据集信息:")
    print(f"X_u_train形状: {X_u_train.shape}")
    print(f"X_y_train形状: {X_y_train.shape}")
    print(f"y_train形状: {y_train.shape}")
    
    # 创建DeepONet模型
    model = DeepONet(
        branch_dim=X_u_train.shape[1],
        trunk_dim=X_y_train.shape[1],
        branch_layers=[128, 64, 32],
        trunk_layers=[128, 64, 32]
    )
    
    # 编译模型
    model.compile(learning_rate=1e-3)  # 使用更大的初始学习率
    
    # 定义回调函数
    callbacks = [
        tf.keras.callbacks.ModelCheckpoint(
            'models/deeponet_best.h5',
            save_best_only=True,
            monitor='val_loss'
        ),
        tf.keras.callbacks.EarlyStopping(
            monitor='val_loss',
            patience=30,
            restore_best_weights=True
        ),
        tf.keras.callbacks.ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.2,
            patience=10,
            min_lr=1e-6
        )
    ]
    
    # 训练模型
    history = model.train(
        X_u_train, X_y_train, y_train,
        batch_size=batch_size,
        epochs=epochs,
        validation_split=0.2,
        callbacks=callbacks
    )
    
    return model, history, (X_u_test, X_y_test, y_test)


if __name__ == "__main__":
    # 如果数据集不存在，运行CH求解器生成数据集
    data_path = "ch_small_dataset.npz"
    if not os.path.exists(data_path):
        print("数据集不存在，请先运行ch_solver.py生成数据集")
        exit(1)
    
    # 训练模型
    model, history, test_data = train_model(data_path, epochs=500, batch_size=16)
    
    # 绘制训练历史
    plt.figure(figsize=(12, 5))
    
    plt.subplot(1, 2, 1)
    plt.plot(history.history['loss'], label='训练损失')
    plt.plot(history.history['val_loss'], label='验证损失')
    plt.yscale('log')
    plt.xlabel('轮次')
    plt.ylabel('损失 (MSE)')
    plt.legend()
    plt.title('训练和验证损失')
    
    plt.subplot(1, 2, 2)
    plt.plot(history.history['mae'], label='训练MAE')
    plt.plot(history.history['val_mae'], label='验证MAE')
    plt.yscale('log')
    plt.xlabel('轮次')
    plt.ylabel('平均绝对误差')
    plt.legend()
    plt.title('训练和验证MAE')
    
    plt.tight_layout()
    plt.savefig('training_history.png')
    plt.close()
    
    print("训练完成，结果已保存。") 