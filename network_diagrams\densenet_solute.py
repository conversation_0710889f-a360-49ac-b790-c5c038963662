import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle, FancyArrowPatch, PathPatch
from matplotlib.path import Path
import matplotlib.colors as mcolors

# 创建画布
fig, ax = plt.subplots(figsize=(12, 8), facecolor='white')

# 设置颜色
colors = {
    'input': mcolors.to_rgb('#C8E6C9'),  # 浅绿色
    'conv': mcolors.to_rgb('#81D4FA'),   # 浅蓝色
    'pool': mcolors.to_rgb('#FFCC80'),   # 浅橙色
    'bn': mcolors.to_rgb('#E1BEE7'),     # 浅紫色
    'relu': mcolors.to_rgb('#FFECB3'),   # 浅黄色
    'gap': mcolors.to_rgb('#EF9A9A'),    # 浅红色
    'dense': mcolors.to_rgb('#B39DDB'),  # 淡紫色
    'output': mcolors.to_rgb('#F48FB1'),  # 粉色
    'attention': mcolors.to_rgb('#B2DFDB'),  # 蓝绿色
    'concat': mcolors.to_rgb('#F0F4C3'),  # 浅黄绿色
    'transition': mcolors.to_rgb('#CFD8DC')  # 浅灰色
}

# 绘制基本框架
def draw_box(x, y, width, height, color, label, fontsize=9):
    rect = Rectangle((x, y), width, height, linewidth=1, edgecolor='black', facecolor=color, alpha=0.8)
    ax.add_patch(rect)
    ax.text(x + width/2, y + height/2, label, ha='center', va='center', fontsize=fontsize, fontweight='bold')

def draw_arrow(x1, y1, x2, y2, style='normal', color='black'):
    if style == 'normal':
        arrow = FancyArrowPatch((x1, y1), (x2, y2), connectionstyle="arc3,rad=0.0", 
                               arrowstyle='->', mutation_scale=15, linewidth=1.5, color=color)
    elif style == 'skip':
        arrow = FancyArrowPatch((x1, y1), (x2, y2), connectionstyle="arc3,rad=0.3", 
                               arrowstyle='->', mutation_scale=15, linewidth=1.5, color=color)
    elif style == 'concat':
        arrow = FancyArrowPatch((x1, y1), (x2, y2), connectionstyle="arc3,rad=0.15", 
                               arrowstyle='->', mutation_scale=15, linewidth=1.5, color='blue')
    ax.add_patch(arrow)

# 绘制DenseNet结构
# 输入层
draw_box(1, 8, 2, 1, colors['input'], "Input\n(h, w, 1)")

# 初始卷积
draw_box(5, 8, 2, 1, colors['conv'], "Conv2D\n7x7, 32")
draw_arrow(3, 8.5, 5, 8.5)
draw_box(5, 6.8, 2, 0.8, colors['bn'], "BatchNorm")
draw_arrow(6, 8, 6, 7.6)
draw_box(5, 5.8, 2, 0.8, colors['relu'], "ReLU")
draw_arrow(6, 6.8, 6, 6.6)
draw_box(5, 4.8, 2, 0.8, colors['pool'], "MaxPool\n3x3")
draw_arrow(6, 5.8, 6, 5.6)

# Dense块1
draw_box(8, 8, 5, 2.5, colors['conv'], "Dense Block 1\n3×[BN→ReLU→Conv2D(3×3)]")
draw_arrow(7, 5.2, 8, 9.2)

# 第一个Dense层内部连接
# 为了简化，我们只展示3个密集连接
draw_box(9, 7.3, 1, 0.5, colors['bn'], "BN")
draw_box(9, 6.7, 1, 0.5, colors['relu'], "ReLU")
draw_box(9, 6.1, 1, 0.5, colors['conv'], "Conv")
draw_arrow(8.5, 7.5, 9, 7.5)
draw_arrow(9.5, 7.3, 9.5, 6.7)
draw_arrow(9.5, 6.7, 9.5, 6.1)

draw_box(10.5, 7.3, 1, 0.5, colors['bn'], "BN")
draw_box(10.5, 6.7, 1, 0.5, colors['relu'], "ReLU")
draw_box(10.5, 6.1, 1, 0.5, colors['conv'], "Conv")
draw_arrow(10, 6.3, 10.5, 7.5, style='concat')
draw_arrow(11, 7.3, 11, 6.7)
draw_arrow(11, 6.7, 11, 6.1)

draw_box(12, 7.3, 1, 0.5, colors['bn'], "BN")
draw_box(12, 6.7, 1, 0.5, colors['relu'], "ReLU")
draw_box(12, 6.1, 1, 0.5, colors['conv'], "Conv")
draw_arrow(11.5, 6.3, 12, 7.5, style='concat')
draw_arrow(12.5, 7.3, 12.5, 6.7)
draw_arrow(12.5, 6.7, 12.5, 6.1)

# 过渡层1
draw_box(8, 4.8, 2, 0.8, colors['transition'], "Transition 1\nBN→Conv→Pool")
draw_arrow(10.5, 6.1, 9, 5.2, style='concat')

# Dense块2
draw_box(12, 4.8, 5, 2.5, colors['conv'], "Dense Block 2\n3×[BN→ReLU→Conv2D(3×3)]")
draw_arrow(10, 5.2, 12, 6.0)

# 第二个Dense层内部连接
draw_box(13, 4.1, 1, 0.5, colors['bn'], "BN")
draw_box(13, 3.5, 1, 0.5, colors['relu'], "ReLU")
draw_box(13, 2.9, 1, 0.5, colors['conv'], "Conv")
draw_arrow(12.5, 4.3, 13, 4.3)
draw_arrow(13.5, 4.1, 13.5, 3.5)
draw_arrow(13.5, 3.5, 13.5, 2.9)

draw_box(14.5, 4.1, 1, 0.5, colors['bn'], "BN")
draw_box(14.5, 3.5, 1, 0.5, colors['relu'], "ReLU")
draw_box(14.5, 2.9, 1, 0.5, colors['conv'], "Conv")
draw_arrow(14, 3.1, 14.5, 4.3, style='concat')
draw_arrow(15, 4.1, 15, 3.5)
draw_arrow(15, 3.5, 15, 2.9)

draw_box(16, 4.1, 1, 0.5, colors['bn'], "BN")
draw_box(16, 3.5, 1, 0.5, colors['relu'], "ReLU")
draw_box(16, 2.9, 1, 0.5, colors['conv'], "Conv")
draw_arrow(15.5, 3.1, 16, 4.3, style='concat')
draw_arrow(16.5, 4.1, 16.5, 3.5)
draw_arrow(16.5, 3.5, 16.5, 2.9)

# 注意力机制
draw_box(18, 8, 3, 1.5, colors['attention'], "Channel Attention\nGAP→Dense→Dense\nReshape→Multiply")
draw_arrow(13, 8.5, 18, 8.5)

draw_box(18, 5.8, 3, 1.5, colors['attention'], "Spatial Attention\nConv2D 7x7\nActivation→Multiply")
draw_arrow(19.5, 8, 19.5, 7.3)

# 全局特征提取
draw_box(18, 4, 3, 1, colors['gap'], "GlobalAvgPool")
draw_arrow(19.5, 5.8, 19.5, 5)

# 全连接层
draw_box(18, 2.5, 3, 1, colors['dense'], "Dense(256)")
draw_arrow(19.5, 4, 19.5, 3.5)

# BatchNorm
draw_box(18, 1.5, 3, 0.7, colors['bn'], "BatchNorm")
draw_arrow(19.5, 2.5, 19.5, 2.2)

# Dropout
draw_box(18, 0.5, 3, 0.7, colors['dense'], "Dropout(0.3)")
draw_arrow(19.5, 1.5, 19.5, 1.2)

# 输出层
draw_box(23, 0.5, 2, 0.7, colors['output'], "Output")
draw_arrow(21, 0.85, 23, 0.85)

# 图例
legend_x = 1
legend_y = 2
legend_height = 0.3
legend_width = 1.5
legend_spacing = 0.4

for i, (key, color) in enumerate(colors.items()):
    row = i % 6
    col = i // 6
    x_pos = legend_x + col * 3
    y_pos = legend_y - row * legend_spacing
    draw_box(x_pos, y_pos, legend_width, legend_height, color, key.capitalize(), fontsize=8)

# 设置坐标轴
ax.set_xlim(0, 26)
ax.set_ylim(0, 10)
ax.axis('off')

# 标题
plt.title('Solute Field - DenseNet Architecture', fontsize=18, fontweight='bold', y=0.98)

# 保存图像
plt.savefig('densenet_solute_field.png', dpi=300, bbox_inches='tight')
plt.close()

print("Concentration field DenseNet architecture saved as 'densenet_solute_field.png'") 