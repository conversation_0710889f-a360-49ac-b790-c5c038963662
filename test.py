import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import os
import pandas as pd
import argparse  # 添加argparse库
import time
import psutil
import GPUtil
from datetime import datetime
import gc  # 导入垃圾回收模块
import pickle
import json


# 内存管理函数
def clear_memory():
    """手动触发垃圾回收，释放内存"""
    gc.collect()
    if tf.config.list_physical_devices('GPU'):
        try:
            # 尝试清理GPU内存
            tf.keras.backend.clear_session()
        except Exception as e:
            print(f"清理GPU内存时出错: {str(e)}")

    # 打印当前内存使用情况
    memory = psutil.virtual_memory()
    print(f"内存清理后使用率: {memory.percent}%")
    print(f"可用内存: {memory.available / 1024 / 1024 / 1024:.2f} GB")


# GPU配置和诊断
def setup_gpu():
    print("TensorFlow版本:", tf.__version__)

    # 检查CUDA是否可用
    print("\nCUDA配置:")
    print("CUDA是否可用:", tf.test.is_built_with_cuda())

    # 检测可用的GPU
    gpus = tf.config.list_physical_devices('GPU')
    if gpus:
        try:
            # 设置GPU显存按需分配
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print("\nGPU配置:")
            print(f"- 检测到 {len(gpus)} 个GPU设备")
            print("- GPU显存已设置为按需分配")

            # 设置TF内存限制
            for gpu in gpus:
                # 限制TensorFlow使用GPU显存的80%，避免OOM
                memory_limit = int(get_gpu_memory(gpu) * 0.8)
                print(f"- 将GPU {gpu} 内存限制设置为 {memory_limit} MB")
                tf.config.set_logical_device_configuration(
                    gpu,
                    [tf.config.LogicalDeviceConfiguration(memory_limit=memory_limit)]
                )

            # 设置GPU为默认设备
            tf.config.set_visible_devices(gpus[0], 'GPU')
            logical_gpus = tf.config.list_logical_devices('GPU')
            print(f"- 可用的逻辑GPU数量: {len(logical_gpus)}")

            # 测试GPU计算
            print("\n执行GPU测试计算...")
            with tf.device('/GPU:0'):
                a = tf.random.normal([1000, 1000])
                b = tf.random.normal([1000, 1000])
                c = tf.matmul(a, b)
            print("GPU测试完成：矩阵乘法运算成功")
            return True

        except RuntimeError as e:
            print("\nGPU配置错误:", str(e))
    else:
        print("\n未检测到GPU，请确保：")
        print("1. 已安装NVIDIA GPU硬件")
        print("2. 已安装最新的NVIDIA驱动程序")
        print("3. 已安装CUDA Toolkit（建议11.8版本）")
        print("4. 已安装cuDNN（与CUDA版本匹配）")
        print("5. 已安装tensorflow-gpu或正确版本的tensorflow")
        print("\n您可以访问以下链接获取安装指南：")
        print("- NVIDIA驱动：https://www.nvidia.com/Download/index.aspx")
        print("- CUDA Toolkit：https://developer.nvidia.com/cuda-toolkit")
        print("- cuDNN：https://developer.nvidia.com/cudnn")
        return False


# 设置混合精度训练
def setup_mixed_precision():
    if tf.config.list_physical_devices('GPU'):
        try:
            tf.keras.mixed_precision.set_global_policy('mixed_float16')
            print("\n混合精度配置:")
            print("- 已启用mixed_float16策略")
            return True
        except Exception as e:
            print("\n设置混合精度失败:", str(e))
            return False
    return False


class DeepONetFreeze(tf.keras.Model):
    def __init__(self, use_attention=True, use_residual=True):
        super(DeepONetFreeze, self).__init__()

        self.use_attention = use_attention
        self.use_residual = use_residual

        # Branch网络 - 处理输入场，增加残差连接和注意力机制
        self.branch_conv1 = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same',
                                                   input_shape=(None, None, 1))
        self.branch_bn1 = tf.keras.layers.BatchNormalization()
        self.branch_conv2 = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same')
        self.branch_bn2 = tf.keras.layers.BatchNormalization()
        self.branch_pool1 = tf.keras.layers.MaxPooling2D((2, 2))

        # 第二个卷积块
        self.branch_conv3 = tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same')
        self.branch_bn3 = tf.keras.layers.BatchNormalization()
        self.branch_conv4 = tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same')
        self.branch_bn4 = tf.keras.layers.BatchNormalization()
        self.branch_pool2 = tf.keras.layers.MaxPooling2D((2, 2))

        # 注意力模块
        if self.use_attention:
            self.channel_attention = self._build_channel_attention(64)
            self.spatial_attention = self._build_spatial_attention()

        # 最终特征提取
        self.branch_conv5 = tf.keras.layers.Conv2D(128, (3, 3), activation='relu', padding='same')
        self.branch_bn5 = tf.keras.layers.BatchNormalization()
        self.branch_gap = tf.keras.layers.GlobalAveragePooling2D()

        # 全连接层
        self.branch_dense1 = tf.keras.layers.Dense(256, activation='relu')
        self.branch_bn6 = tf.keras.layers.BatchNormalization()
        self.branch_dropout = tf.keras.layers.Dropout(0.3)
        self.branch_dense2 = tf.keras.layers.Dense(100, activation='relu')
        self.branch_output = tf.keras.layers.Dense(50)

        # Trunk网络 - 处理空间坐标，增加更多层和残差连接
        self.trunk_dense1 = tf.keras.layers.Dense(100, activation='relu', input_shape=(2,))
        self.trunk_bn1 = tf.keras.layers.BatchNormalization()
        self.trunk_dense2 = tf.keras.layers.Dense(100, activation='relu')
        self.trunk_bn2 = tf.keras.layers.BatchNormalization()

        if self.use_residual:
            self.trunk_res_dense = tf.keras.layers.Dense(100, activation='linear')

        self.trunk_dense3 = tf.keras.layers.Dense(100, activation='relu')
        self.trunk_bn3 = tf.keras.layers.BatchNormalization()
        self.trunk_output = tf.keras.layers.Dense(50, activation='linear')

    def _build_channel_attention(self, channels):
        """构建通道注意力模块"""
        return tf.keras.Sequential([
            tf.keras.layers.GlobalAveragePooling2D(),
            tf.keras.layers.Dense(channels // 4, activation='relu'),
            tf.keras.layers.Dense(channels, activation='sigmoid'),
            tf.keras.layers.Reshape((1, 1, channels))
        ])

    def _build_spatial_attention(self):
        """构建空间注意力模块"""
        return tf.keras.Sequential([
            tf.keras.layers.Conv2D(1, (7, 7), padding='same', activation='sigmoid')
        ])

    def call(self, inputs):
        # 解包输入
        field_input, coords = inputs

        # Branch网络处理场数据
        x = self.branch_conv1(field_input)
        x = self.branch_bn1(x)
        x = self.branch_conv2(x)
        x = self.branch_bn2(x)
        x = self.branch_pool1(x)

        x = self.branch_conv3(x)
        x = self.branch_bn3(x)
        x = self.branch_conv4(x)
        x = self.branch_bn4(x)
        x = self.branch_pool2(x)

        # 应用注意力机制
        if self.use_attention:
            # 通道注意力
            ca = self.channel_attention(x)
            x = tf.multiply(x, ca)

            # 空间注意力
            sa = self.spatial_attention(x)
            x = tf.multiply(x, sa)

        x = self.branch_conv5(x)
        x = self.branch_bn5(x)
        x = self.branch_gap(x)

        x = self.branch_dense1(x)
        x = self.branch_bn6(x)
        x = self.branch_dropout(x)
        x = self.branch_dense2(x)
        branch_output = self.branch_output(x)

        # Trunk网络处理坐标
        y = self.trunk_dense1(coords)
        y = self.trunk_bn1(y)
        y = self.trunk_dense2(y)
        y = self.trunk_bn2(y)

        # 残差连接
        if self.use_residual:
            residual = self.trunk_res_dense(coords)
            y = y + residual

        y = self.trunk_dense3(y)
        y = self.trunk_bn3(y)
        trunk_output = self.trunk_output(y)

        # DeepONet核心运算
        return tf.tensordot(branch_output, trunk_output, axes=([1], [1]))


def load_freeze_data(phi_data_dir, temp_data_dir, solute_data_dir, start_step=0, end_step=3050, stride=1,
                     max_samples=100):
    """从不同目录加载相场、温度场和溶质场数据，使用stride控制采样步长，max_samples控制最大样本数"""
    phi_data = []
    temp_data = []
    solute_data = []

    # 计算实际需要加载的步数范围
    steps = list(range(start_step, end_step + 1, stride))

    # 如果样本数量超过最大限制，进一步增加采样步长
    if len(steps) > max_samples:
        sample_stride = len(steps) // max_samples + 1
        steps = steps[::sample_stride]
        print(f"样本数量超过最大限制({max_samples})，进一步增加采样步长为: {stride * sample_stride}")

    print(f"将加载 {len(steps)} 个样本，采样步长为 {stride}")

    for i in steps:
        try:
            # 使用统一的文件命名格式，但从不同目录加载
            phi_file = os.path.join(phi_data_dir, f"相场第{i}步.csv")
            temp_file = os.path.join(temp_data_dir, f"温度场第{i}步.csv")
            solute_file = os.path.join(solute_data_dir, f"溶质场第{i}步.csv")

            # 检查文件是否存在
            if not os.path.exists(phi_file):
                print(f"警告: 文件不存在 {phi_file}")
                continue
            if not os.path.exists(temp_file):
                print(f"警告: 文件不存在 {temp_file}")
                continue
            if not os.path.exists(solute_file):
                print(f"警告: 文件不存在 {solute_file}")
                continue

            # 加载相场、温度场和溶质场数据，并转换为float32以节省内存
            phi = pd.read_csv(phi_file, header=None, encoding='utf-8').values.astype(np.float32)
            temp = pd.read_csv(temp_file, header=None, encoding='utf-8').values.astype(np.float32)
            solute = pd.read_csv(solute_file, header=None, encoding='utf-8').values.astype(np.float32)

            print(f"成功加载第{i}步数据: 相场形状{phi.shape}, 温度场形状{temp.shape}, 溶质场形状{solute.shape}")

            phi_data.append(phi)
            temp_data.append(temp)
            solute_data.append(solute)

            # 每加载10个样本，打印一次内存使用情况
            if len(phi_data) % 10 == 0:
                memory = psutil.virtual_memory()
                print(f"已加载 {len(phi_data)} 个样本，当前内存使用率: {memory.percent}%")

                # 如果内存使用率过高，尝试清理内存
                if memory.percent > 80:
                    print("内存使用率过高，尝试清理内存...")
                    clear_memory()

        except Exception as e:
            print(f"加载第{i}步数据时出错: {str(e)}")

    if not phi_data or not temp_data or not solute_data:
        raise ValueError("未能加载任何数据，请检查文件路径和格式")

    print(f"数据加载完成，共加载 {len(phi_data)} 个样本")
    return np.array(phi_data, dtype=np.float32), np.array(temp_data, dtype=np.float32), np.array(solute_data,
                                                                                                 dtype=np.float32)


def create_model(input_shape, num_points, output_dim, model_type='phase', use_cosine_decay=False):
    """创建改进版DeepONet模型

    参数:
        input_shape: 输入场的形状
        num_points: 坐标点数量
        output_dim: 输出维度
        model_type: 模型类型，可选 'phase', 'temp', 'solute'，用于调整不同场的模型结构
        use_cosine_decay: 是否使用余弦退火学习率，如果为False则使用固定学习率
    """
    # 指定在GPU上创建模型
    with tf.device('/GPU:0'):
        # 输入层
        field_input = tf.keras.layers.Input(shape=input_shape)

        # 创建更强大的骨干网络，针对不同类型场使用适当的网络架构
        if model_type == 'temp':
            # 温度场采用增强版UNet结构，更适合捕捉全局和局部特征
            # 编码器部分 - 增加深度和特征通道
            conv1 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(field_input)
            conv1 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv1)
            conv1 = tf.keras.layers.BatchNormalization()(conv1)
            conv1 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(conv1)
            conv1 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv1)
            conv1 = tf.keras.layers.BatchNormalization()(conv1)
            pool1 = tf.keras.layers.MaxPooling2D((2, 2))(conv1)

            conv2 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(pool1)
            conv2 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv2)
            conv2 = tf.keras.layers.BatchNormalization()(conv2)
            conv2 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(conv2)
            conv2 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv2)
            conv2 = tf.keras.layers.BatchNormalization()(conv2)
            pool2 = tf.keras.layers.MaxPooling2D((2, 2))(conv2)

            conv3 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(pool2)
            conv3 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv3)
            conv3 = tf.keras.layers.BatchNormalization()(conv3)
            conv3 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(conv3)
            conv3 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv3)
            conv3 = tf.keras.layers.BatchNormalization()(conv3)
            pool3 = tf.keras.layers.MaxPooling2D((2, 2))(conv3)

            # 瓶颈层 - 增加特征提取能力
            bottleneck = tf.keras.layers.Conv2D(512, (3, 3), activation=None, padding='same')(pool3)
            bottleneck = tf.keras.layers.LeakyReLU(alpha=0.2)(bottleneck)
            bottleneck = tf.keras.layers.BatchNormalization()(bottleneck)
            bottleneck = tf.keras.layers.Conv2D(512, (3, 3), activation=None, padding='same')(bottleneck)
            bottleneck = tf.keras.layers.LeakyReLU(alpha=0.2)(bottleneck)
            bottleneck = tf.keras.layers.BatchNormalization()(bottleneck)

            # 添加空间和通道注意力机制 - 增强特征选择能力
            # 通道注意力
            se = tf.keras.layers.GlobalAveragePooling2D()(bottleneck)
            se = tf.keras.layers.Dense(512 // 16, activation='relu')(se)
            se = tf.keras.layers.Dense(512, activation='sigmoid')(se)
            se = tf.keras.layers.Reshape((1, 1, 512))(se)
            bottleneck = tf.keras.layers.Multiply()([bottleneck, se])

            # 空间注意力
            spatial = tf.keras.layers.Conv2D(1, (7, 7), padding='same', activation='sigmoid')(bottleneck)
            bottleneck = tf.keras.layers.Multiply()([bottleneck, spatial])

            # 解码器部分 - 增加更多特征融合
            up3 = tf.keras.layers.Conv2DTranspose(256, (2, 2), strides=(2, 2), padding='same')(bottleneck)
            concat3 = tf.keras.layers.Concatenate()([up3, conv3])
            conv6 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(concat3)
            conv6 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv6)
            conv6 = tf.keras.layers.BatchNormalization()(conv6)
            conv6 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(conv6)
            conv6 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv6)
            conv6 = tf.keras.layers.BatchNormalization()(conv6)

            up2 = tf.keras.layers.Conv2DTranspose(128, (2, 2), strides=(2, 2), padding='same')(conv6)
            concat2 = tf.keras.layers.Concatenate()([up2, conv2])
            conv7 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(concat2)
            conv7 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv7)
            conv7 = tf.keras.layers.BatchNormalization()(conv7)
            conv7 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(conv7)
            conv7 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv7)
            conv7 = tf.keras.layers.BatchNormalization()(conv7)

            up1 = tf.keras.layers.Conv2DTranspose(64, (2, 2), strides=(2, 2), padding='same')(conv7)
            concat1 = tf.keras.layers.Concatenate()([up1, conv1])
            conv8 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(concat1)
            conv8 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv8)
            conv8 = tf.keras.layers.BatchNormalization()(conv8)
            conv8 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(conv8)
            conv8 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv8)
            conv8 = tf.keras.layers.BatchNormalization()(conv8)

            # 全局特征提取 - 使用多尺度特征融合
            # 1. 全局平均池化
            gap = tf.keras.layers.GlobalAveragePooling2D()(conv8)

            # 2. 添加多尺度特征
            gap_bottleneck = tf.keras.layers.GlobalAveragePooling2D()(bottleneck)
            gap_conv6 = tf.keras.layers.GlobalAveragePooling2D()(conv6)
            gap_conv7 = tf.keras.layers.GlobalAveragePooling2D()(conv7)

            # 3. 特征融合
            multi_scale = tf.keras.layers.Concatenate()([gap, gap_bottleneck, gap_conv6, gap_conv7])

            # 全连接层 - 增加网络深度和宽度
            x = tf.keras.layers.Dense(1024, activation=None)(multi_scale)
            x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Dropout(0.5)(x)  # 增加dropout比例

            # 残差连接
            res = tf.keras.layers.Dense(1024)(multi_scale)
            x = tf.keras.layers.add([x, res])
            x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)

            x = tf.keras.layers.Dense(512, activation=None)(x)
            x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Dropout(0.4)(x)

            # 添加另一个残差连接
            res2 = tf.keras.layers.Dense(512)(x)
            x = tf.keras.layers.Dense(512, activation=None)(x)
            x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.add([x, res2])

            branch_output = tf.keras.layers.Dense(output_dim)(x)

        elif model_type == 'phase':
            # 相场模型 - 使用ResNet风格的架构
            # 初始卷积
            x = tf.keras.layers.Conv2D(32, (7, 7), strides=(2, 2), padding='same')(field_input)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Activation('relu')(x)
            x = tf.keras.layers.MaxPooling2D((3, 3), strides=(2, 2), padding='same')(x)

            # 残差块1
            shortcut = x
            x = tf.keras.layers.Conv2D(32, (3, 3), padding='same')(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Activation('relu')(x)
            x = tf.keras.layers.Conv2D(32, (3, 3), padding='same')(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.add([x, shortcut])
            x = tf.keras.layers.Activation('relu')(x)

            # 残差块2
            shortcut = tf.keras.layers.Conv2D(64, (1, 1), strides=(2, 2))(x)
            shortcut = tf.keras.layers.BatchNormalization()(shortcut)

            x = tf.keras.layers.Conv2D(64, (3, 3), strides=(2, 2), padding='same')(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Activation('relu')(x)
            x = tf.keras.layers.Conv2D(64, (3, 3), padding='same')(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.add([x, shortcut])
            x = tf.keras.layers.Activation('relu')(x)

            # 注意力机制 - 通道注意力
            se = tf.keras.layers.GlobalAveragePooling2D()(x)
            se = tf.keras.layers.Dense(64 // 4, activation='relu')(se)
            se = tf.keras.layers.Dense(64, activation='sigmoid')(se)
            x = tf.keras.layers.Multiply()([x, tf.keras.layers.Reshape((1, 1, 64))(se)])

            # 空间注意力
            spatial = tf.keras.layers.Conv2D(1, (7, 7), padding='same', activation='sigmoid')(x)
            x = tf.keras.layers.Multiply()([x, spatial])

            # 全局特征提取
            x = tf.keras.layers.GlobalAveragePooling2D()(x)

            # 全连接层
            x = tf.keras.layers.Dense(256, activation='relu')(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Dropout(0.3)(x)
            branch_output = tf.keras.layers.Dense(output_dim)(x)

        else:  # solute模型
            # 溶质场模型 - 使用DenseNet风格的架构
            # 初始卷积
            x = tf.keras.layers.Conv2D(32, (7, 7), strides=(2, 2), padding='same')(field_input)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Activation('relu')(x)
            x = tf.keras.layers.MaxPooling2D((3, 3), strides=(2, 2), padding='same')(x)

            # Dense块1
            for _ in range(3):
                x1 = tf.keras.layers.BatchNormalization()(x)
                x1 = tf.keras.layers.Activation('relu')(x1)
                x1 = tf.keras.layers.Conv2D(16, (3, 3), padding='same')(x1)
                x = tf.keras.layers.Concatenate()([x, x1])

            # 过渡层1
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Activation('relu')(x)
            x = tf.keras.layers.Conv2D(64, (1, 1), padding='same')(x)
            x = tf.keras.layers.AveragePooling2D((2, 2), strides=(2, 2))(x)

            # Dense块2
            for _ in range(3):
                x1 = tf.keras.layers.BatchNormalization()(x)
                x1 = tf.keras.layers.Activation('relu')(x1)
                x1 = tf.keras.layers.Conv2D(16, (3, 3), padding='same')(x1)
                x = tf.keras.layers.Concatenate()([x, x1])

            # 注意力机制
            se = tf.keras.layers.GlobalAveragePooling2D()(x)
            se = tf.keras.layers.Dense(x.shape[-1] // 4, activation='relu')(se)
            se = tf.keras.layers.Dense(x.shape[-1], activation='sigmoid')(se)
            x = tf.keras.layers.Multiply()([x, tf.keras.layers.Reshape((1, 1, x.shape[-1]))(se)])

            # 全局特征提取
            x = tf.keras.layers.GlobalAveragePooling2D()(x)

            # 全连接层
            x = tf.keras.layers.Dense(256, activation='relu')(x)
            x = tf.keras.layers.BatchNormalization()(x)
            x = tf.keras.layers.Dropout(0.3)(x)
            branch_output = tf.keras.layers.Dense(output_dim)(x)

        # 创建模型
        model = tf.keras.Model(inputs=field_input, outputs=branch_output)

    # 配置优化器与学习率
    if model_type == 'temp':
        # 温度场使用优化的学习率策略
        if use_cosine_decay:
            # 使用余弦退火学习率 - 增加初始学习率并调整衰减步数
            initial_lr = 1e-4  # 增大初始学习率
            lr_schedule = tf.keras.optimizers.schedules.CosineDecay(
                initial_learning_rate=initial_lr,
                decay_steps=2000,  # 增加衰减步数
                alpha=1e-6
            )
            optimizer = tf.keras.optimizers.Adam(learning_rate=lr_schedule, clipnorm=0.5)  # 直接在优化器中设置梯度裁剪
        else:
            # 使用固定学习率 - 但稍微增大
            optimizer = tf.keras.optimizers.Adam(learning_rate=1e-4, clipnorm=0.5)  # 直接在优化器中设置梯度裁剪
    else:
        # 相场和溶质场使用自适应学习率
        lr = 1e-4
        optimizer = tf.keras.optimizers.Adam(learning_rate=lr, clipnorm=1.0)  # 直接在优化器中设置梯度裁剪

    if tf.config.list_physical_devices('GPU'):
        # 在GPU上使用混合精度
        optimizer = tf.keras.mixed_precision.LossScaleOptimizer(optimizer)

    # 编译模型 - 使用自定义损失函数组合
    if model_type == 'temp':
        # 温度场使用改进的组合损失函数
        def combined_loss(y_true, y_pred):
            # 确保输入数据类型一致，统一使用float32
            y_true = tf.cast(y_true, tf.float32)
            y_pred = tf.cast(y_pred, tf.float32)

            # 增加MSE权重，减少MAE权重
            mse = tf.keras.losses.MeanSquaredError()(y_true, y_pred)
            mae = tf.keras.losses.MeanAbsoluteError()(y_true, y_pred)

            # 添加Huber损失，更好地处理异常值
            huber = tf.keras.losses.Huber(delta=1.0)(y_true, y_pred)

            # 组合损失函数
            return 0.6 * mse + 0.2 * mae + 0.2 * huber

        model.compile(optimizer=optimizer, loss=combined_loss, metrics=['mae', 'mse'])
    else:
        model.compile(optimizer=optimizer, loss='mse', metrics=['mae'])

    return model


def prepare_training_data(fields, dx=1e-7, dy=1e-7, downsample_factor=1, field_type='phase',
                          use_data_augmentation=True):
    """准备训练数据，采用多种方式生成更多样本，支持降采样以减少内存使用，并添加数据增强

    参数:
        fields: 输入场数据列表
        dx, dy: 坐标网格间距
        downsample_factor: 降采样因子
        field_type: 场类型，可选 'phase', 'temp', 'solute'
        use_data_augmentation: 是否使用数据增强
    """
    # 检查数据形状
    if len(fields) < 2:
        raise ValueError("至少需要两个时间步的数据来创建输入-输出对")

    print(f"准备{field_type}数据: 有{len(fields)}个时间步，每个场的形状为{fields[0].shape}")

    # 如果是温度场数据，进行改进的归一化处理
    if field_type == 'temp':
        print("对温度场数据进行改进的归一化处理...")

        # 1. 计算所有温度场的全局统计量
        all_data = np.concatenate([field.flatten() for field in fields])
        global_min = np.min(all_data)
        global_max = np.max(all_data)
        global_mean = np.mean(all_data)
        global_std = np.std(all_data)
        print(f"温度场数据统计: 最小值 {global_min}, 最大值 {global_max}, 均值 {global_mean}, 标准差 {global_std}")

        # 2. 检测并处理异常值
        q1 = np.percentile(all_data, 1)  # 下1%分位数
        q99 = np.percentile(all_data, 99)  # 上99%分位数
        iqr_range = q99 - q1
        lower_bound = q1 - 1.5 * iqr_range
        upper_bound = q99 + 1.5 * iqr_range
        print(f"异常值处理范围: 下界 {lower_bound}, 上界 {upper_bound}")

        # 3. 使用Z-score归一化，更适合温度场数据
        fields = [(field - global_mean) / (global_std + 1e-10) for field in fields]
        print("温度场Z-score归一化完成")

        # 保存归一化参数，用于预测时的反归一化
        norm_params = {
            'min': global_min,
            'max': global_max,
            'mean': global_mean,
            'std': global_std,
            'lower_bound': lower_bound,
            'upper_bound': upper_bound
        }

        # 释放内存
        del all_data
        gc.collect()
    else:
        norm_params = None

    # 如果指定了降采样因子，对场数据进行降采样
    if downsample_factor > 1:
        downsampled_fields = []
        for field in fields:
            try:
                # 使用双三次插值进行降采样，保留更多细节
                from scipy.ndimage import zoom
                zoom_factor = 1.0 / downsample_factor
                downsampled = zoom(field, zoom_factor, order=3)  # order=3为双三次插值
                downsampled_fields.append(downsampled)
            except Exception as e:
                print(f"双三次插值降采样失败: {str(e)}，使用简单步长降采样")
                # 使用简单的步长降采样
                downsampled = field[::downsample_factor, ::downsample_factor]
                downsampled_fields.append(downsampled)
        fields = downsampled_fields
        print(f"已对场数据进行 {downsample_factor}倍 降采样，新形状为 {fields[0].shape}")
        # 释放内存
        del downsampled_fields
        gc.collect()

    # 生成坐标网格
    m, n = fields[0].shape
    x = np.linspace(0, m * dx, m)
    y = np.linspace(0, n * dy, n)
    X, Y = np.meshgrid(x, y)
    coords = np.stack([X.flatten(), Y.flatten()], axis=-1)  # 形状: (m*n, 2)

    # 准备输入-输出对
    X_train = []
    y_train = []

    # 每个时间步作为输入，下一步作为输出
    for i in range(len(fields) - 1):
        # 将场数据重塑为合适的形状 (batch_size, height, width, channels)
        input_field = fields[i].reshape(-1, fields[i].shape[0], fields[i].shape[1], 1)

        # 添加到训练数据中
        X_train.append(input_field)
        y_train.append(fields[i + 1].flatten())  # 将输出平铺为一维向量

        # 数据增强 - 如果启用
        if use_data_augmentation:
            # 1. 水平翻转
            flipped_h = np.flip(input_field, axis=2)
            X_train.append(flipped_h)
            y_train.append(np.flip(fields[i + 1], axis=1).flatten())

            # 2. 垂直翻转
            flipped_v = np.flip(input_field, axis=1)
            X_train.append(flipped_v)
            y_train.append(np.flip(fields[i + 1], axis=0).flatten())

            # 3. 旋转90度 - 温度场也可以旋转，因为我们已经归一化了
            rotated = np.rot90(input_field.squeeze(0), k=1).reshape(input_field.shape)
            X_train.append(rotated)
            y_train.append(np.rot90(fields[i + 1], k=1).flatten())

            # 4. 添加微小噪声 - 针对不同场类型调整噪声级别
            if field_type == 'phase':
                noise_level = 0.02
            elif field_type == 'temp':
                # 温度场使用更小的噪声，避免干扰温度梯度
                noise_level = 0.01
            else:  # solute
                noise_level = 0.005

            noisy = input_field + np.random.normal(0, noise_level, input_field.shape)
            X_train.append(noisy)
            y_train.append(fields[i + 1].flatten())

            # 5. 对温度场添加额外的增强 - 小幅度缩放
            if field_type == 'temp':
                # 随机小幅度缩放
                scale_factors = [0.95, 0.975, 1.025, 1.05]
                for scale in scale_factors:
                    try:
                        from scipy.ndimage import zoom
                        scaled = zoom(input_field.squeeze(0), (scale, scale, 1), order=1)
                        # 调整回原始大小
                        if scaled.shape[0] != input_field.shape[1] or scaled.shape[1] != input_field.shape[2]:
                            scaled = zoom(scaled, (input_field.shape[1] / scaled.shape[0],
                                                   input_field.shape[2] / scaled.shape[1], 1), order=1)
                        X_train.append(scaled.reshape(input_field.shape))

                        # 对应的输出也需要缩放
                        scaled_output = zoom(fields[i + 1], (scale, scale), order=1)
                        if scaled_output.shape[0] != fields[i + 1].shape[0] or scaled_output.shape[1] != \
                                fields[i + 1].shape[1]:
                            scaled_output = zoom(scaled_output, (fields[i + 1].shape[0] / scaled_output.shape[0],
                                                                 fields[i + 1].shape[1] / scaled_output.shape[1]),
                                                 order=1)
                        y_train.append(scaled_output.flatten())
                    except Exception as e:
                        print(f"缩放增强失败: {str(e)}")

        # 定期释放内存
        if i % 10 == 0 and i > 0:
            gc.collect()

    print(f"准备完成: 生成了{len(X_train)}个训练样本")

    # 将数据转换为NumPy数组，确保使用float32类型
    X_branch = np.array(X_train, dtype=np.float32)
    y_train = np.array(y_train, dtype=np.float32)

    # 释放内存
    del X_train
    del fields
    gc.collect()

    # 确保X_branch的形状正确 (n_samples, height, width, channels)
    if len(X_branch.shape) > 4:
        X_branch = X_branch.squeeze(1)  # 移除多余的维度

    print(f"X_branch shape: {X_branch.shape}")
    print(f"y_train shape: {y_train.shape}")

    # 打印内存使用情况
    memory = psutil.virtual_memory()
    print(f"训练数据准备完成后内存使用率: {memory.percent}%")
    X_size = X_branch.nbytes / (1024 * 1024)
    y_size = y_train.nbytes / (1024 * 1024)
    print(f"X_branch 内存占用: {X_size:.2f} MB")
    print(f"y_train 内存占用: {y_size:.2f} MB")

    return X_branch, coords, y_train, norm_params


def visualize_prediction(true_field, pred_field, field_type, data_dir, reshape_pred=True, save_dir=None):
    """可视化预测结果

    参数:
        true_field: 真实场数据
        pred_field: 预测场数据
        field_type: 场类型，用于命名保存文件
        data_dir: 数据目录，用于确定保存位置
        reshape_pred: 是否需要重塑预测场数据，默认为True
        save_dir: 自定义保存目录，若提供则优先使用
    """
    # 将预测结果重塑回原始场的形状（如果需要）
    m, n = true_field.shape
    if reshape_pred:
        try:
            # 确保数据类型兼容
            pred_field = pred_field.astype(np.float32)

            pred_field_reshaped = pred_field.reshape(m, n)
            print(f"成功将预测结果重塑为 {m}x{n} 的形状")
        except Exception as e:
            print(f"重塑预测结果时出错: {str(e)}")
            print(f"真实场形状: {true_field.shape}, 预测场形状: {pred_field.shape}")
            # 尝试使用插值方法调整大小
            try:
                from scipy.ndimage import zoom
                # 确保数据类型兼容
                pred_field = pred_field.astype(np.float32)

                if len(pred_field.shape) > 1:
                    zoom_factor = (m / pred_field.shape[0], n / pred_field.shape[1])
                    print(f"尝试使用插值方法调整大小，缩放因子: {zoom_factor}")
                    pred_field_reshaped = zoom(pred_field, zoom_factor, order=1)
                else:
                    # 如果是一维数组，先尝试重塑为合理的二维形状
                    side_len = int(np.sqrt(pred_field.shape[0]))
                    if side_len * side_len == pred_field.shape[0]:
                        # 如果是完全平方数，可以重塑为正方形
                        temp = pred_field.reshape(side_len, side_len)
                        zoom_factor = (m / side_len, n / side_len)
                        print(f"尝试使用插值方法调整大小，缩放因子: {zoom_factor}")
                        pred_field_reshaped = zoom(temp, zoom_factor, order=1)
                    else:
                        # 否则，尝试找到最接近的因子
                        print("尝试找到合适的重塑形状")
                        for i in range(1, pred_field.shape[0]):
                            if pred_field.shape[0] % i == 0:
                                j = pred_field.shape[0] // i
                                if abs(i / j - m / n) < 0.5:  # 如果宽高比接近
                                    temp = pred_field.reshape(i, j)
                                    zoom_factor = (m / i, n / j)
                                    print(f"找到合适的重塑形状: {i}x{j}，缩放因子: {zoom_factor}")
                                    pred_field_reshaped = zoom(temp, zoom_factor, order=1)
                                    break
                        else:
                            # 如果找不到合适的因子，直接使用零填充
                            print("无法找到合适的重塑形状，使用零填充")
                            pred_field_reshaped = np.zeros((m, n), dtype=np.float32)
                            min_size = min(pred_field.shape[0], m * n)
                            pred_field_reshaped.flat[:min_size] = pred_field[:min_size]
            except Exception as e2:
                print(f"插值调整大小也失败: {str(e2)}")
                # 如果所有方法都失败，则使用零填充
                pred_field_reshaped = np.zeros((m, n), dtype=np.float32)
                pred_field_flat = pred_field.flatten()
                min_size = min(pred_field_flat.size, m * n)
                pred_field_reshaped.flat[:min_size] = pred_field_flat[:min_size]
                print(f"使用零填充方法创建了 {m}x{n} 的预测场")
    else:
        # 如果不需要重塑，直接使用预测场数据
        # 但仍然检查形状是否匹配
        if pred_field.shape != true_field.shape:
            print(f"警告：预测场形状 {pred_field.shape} 与真实场形状 {true_field.shape} 不匹配")
            # 尝试调整大小
            try:
                from scipy.ndimage import zoom
                # 确保数据类型兼容
                pred_field = pred_field.astype(np.float32)

                zoom_factor = (m / pred_field.shape[0], n / pred_field.shape[1])
                print(f"尝试使用插值方法调整大小，缩放因子: {zoom_factor}")
                pred_field_reshaped = zoom(pred_field, zoom_factor, order=1)
            except Exception as e:
                print(f"调整大小失败: {str(e)}")
                # 使用零填充
                pred_field_reshaped = np.zeros_like(true_field, dtype=np.float32)
                # 将预测结果放在中心位置
                start_row = (m - pred_field.shape[0]) // 2
                start_col = (n - pred_field.shape[1]) // 2
                end_row = min(start_row + pred_field.shape[0], m)
                end_col = min(start_col + pred_field.shape[1], n)
                pred_height = min(pred_field.shape[0], m - start_row)
                pred_width = min(pred_field.shape[1], n - start_col)
                pred_field_reshaped[start_row:end_row, start_col:end_col] = pred_field[:pred_height, :pred_width]
        else:
            pred_field_reshaped = pred_field
            print(f"不进行重塑，直接使用预测场数据，形状为: {pred_field_reshaped.shape}")

    # 确定保存目录
    if save_dir:
        # 如果提供了自定义保存目录，则使用它
        base_save_dir = save_dir
    else:
        # 否则使用数据目录的父目录
        base_save_dir = os.path.dirname(data_dir)

    # 根据场类型创建子目录
    if field_type == "phase field":
        final_save_dir = os.path.join(base_save_dir, "PhaseField_Images")
    elif field_type == "temperature field":
        final_save_dir = os.path.join(base_save_dir, "TemperatureField_Images")
    else:  # concentration field
        final_save_dir = os.path.join(base_save_dir, "ConcentrationField_Images")

    # 确保目录存在
    os.makedirs(final_save_dir, exist_ok=True)

    # 创建时间戳，确保每次保存的文件名不同
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    # 使用相同的色标范围
    vmin = min(np.min(true_field), np.min(pred_field_reshaped))
    vmax = max(np.max(true_field), np.max(pred_field_reshaped))

    im1 = ax1.imshow(true_field, cmap='jet', vmin=vmin, vmax=vmax)
    ax1.set_title('Ground Truth Field')
    plt.colorbar(im1, ax=ax1)

    im2 = ax2.imshow(pred_field_reshaped, cmap='jet', vmin=vmin, vmax=vmax)
    ax2.set_title('Predicted Field')
    plt.colorbar(im2, ax=ax2)

    # 使用英文标题避免中文显示问题
    field_title_map = {
        "phase field": "Phase Field",
        "temperature field": "Temperature Field",
        "concentration field": "Concentration Field"
    }
    plt.suptitle(field_title_map.get(field_type, field_type))
    save_path = os.path.join(final_save_dir, f'{field_type.replace(" ", "_")}_prediction_{timestamp}.png')
    plt.savefig(save_path)
    print(f"已保存预测结果到: {save_path}")
    plt.close()

    return save_path


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='相变模拟训练参数')

    # 添加训练轮数参数
    parser.add_argument('--phi_epochs', type=int, default=20,
                        help='相场模型训练轮数 (默认: 300)')
    parser.add_argument('--temp_epochs', type=int, default=20,
                        help='温度场模型训练轮数 (默认: 300)')
    parser.add_argument('--solute_epochs', type=int, default=20,
                        help='浓度场模型训练轮数 (默认: 300)')
    parser.add_argument('--coupled_epochs', type=int, default=10,
                        help='耦合模型训练轮数 (默认: 200)')

    # 添加批次大小参数
    parser.add_argument('--batch_size', type=int, default=2,
                        help='训练批次大小 (默认: 2)')

    # 添加数据路径参数
    parser.add_argument('--phi_data_dir', type=str, default='E:/Freezedata/PhaseField_Data/',
                        help='相场数据目录路径')
    parser.add_argument('--temp_data_dir', type=str, default='E:/Freezedata/TemperatureField_Data/',
                        help='温度场数据目录路径')
    parser.add_argument('--solute_data_dir', type=str, default='E:/Freezedata/ConcentrationField_Data/',
                        help='溶质场数据目录路径')

    # 添加模型保存/加载参数
    parser.add_argument('--model_dir', type=str, default='./models',
                        help='模型保存/加载目录 (默认: ./models)')
    parser.add_argument('--load_models', action='store_true',
                        help='是否加载已有模型而不重新训练 (默认: False)')
    parser.add_argument('--save_models', action='store_false',
                        help='是否保存训练后的模型 (默认: False)')

    # 添加早停控制参数
    parser.add_argument('--use_early_stopping', action='store_true',
                        help='是否使用早停机制 (默认: False)')
    parser.add_argument('--patience', type=int, default=20,
                        help='早停耐心值，即多少轮loss不下降后停止训练 (默认: 20)')

    # 添加数据加载控制参数
    parser.add_argument('--stride', type=int, default=10,
                        help='数据加载步长，值越大内存占用越小 (默认: 10)')
    parser.add_argument('--max_samples', type=int, default=5000,
                        help='最大加载样本数量 (默认: 5000)')
    parser.add_argument('--start_step', type=int, default=0,
                        help='起始步数 (默认: 0)')
    parser.add_argument('--end_step', type=int, default=3050,
                        help='结束步数 (默认: 3050)')
    parser.add_argument('--downsample', type=int, default=4,
                        help='场数据空间降采样因子，值越大内存占用越小但精度降低 (默认: 4)')

    # 添加模型架构控制参数
    parser.add_argument('--use_attention', action='store_true', default=True,
                        help='是否使用注意力机制 (默认: True)')
    parser.add_argument('--use_residual', action='store_true', default=True,
                        help='是否使用残差连接 (默认: True)')
    parser.add_argument('--use_data_augmentation', action='store_true', default=True,
                        help='是否使用数据增强 (默认: True)')

    # 添加学习率调度器参数
    parser.add_argument('--use_cosine_decay', action='store_true', default=False,
                        help='是否使用余弦退火学习率调度器 (默认: False)')

    # 添加耦合模型相关参数
    parser.add_argument('--train_coupled', action='store_true',
                        help='是否训练耦合模型 (默认: False)')
    parser.add_argument('--coupled_weight_phi', type=float, default=1.0,
                        help='耦合模型中相场损失权重 (默认: 1.0)')
    parser.add_argument('--coupled_weight_temp', type=float, default=1.0,
                        help='耦合模型中温度场损失权重 (默认: 1.0)')
    parser.add_argument('--coupled_weight_solute', type=float, default=1.0,
                        help='耦合模型中溶质场损失权重 (默认: 1.0)')

    # 添加多时间步预测参数
    parser.add_argument('--generate_multi_step', action='store_false',
                        help='是否生成多时间步预测结果对比图 (默认: False)')
    parser.add_argument('--multi_step_start', type=int, default=0,
                        help='多时间步预测的起始步数 (默认: 0)')
    parser.add_argument('--multi_step_end', type=int, default=3000,
                        help='多时间步预测的结束步数 (默认: 3000)')
    parser.add_argument('--multi_step_interval', type=int, default=500,
                        help='多时间步预测的步数间隔 (默认: 500)')

    return parser.parse_args()


class GPUMonitor(tf.keras.callbacks.Callback):
    def __init__(self):
        super(GPUMonitor, self).__init__()
        self.start_time = None
        self.epoch_times = []
        self.batch_times = []
        self.last_batch_time = None

    def on_epoch_begin(self, epoch, logs=None):
        self.start_time = time.time()
        print(f"\n轮次 {epoch + 1} 开始...")
        self._print_gpu_stats()

    def on_epoch_end(self, epoch, logs=None):
        end_time = time.time()
        epoch_time = end_time - self.start_time
        self.epoch_times.append(epoch_time)

        # 计算平均每批次时间
        avg_batch_time = sum(self.batch_times) / len(self.batch_times) if self.batch_times else 0
        self.batch_times = []  # 重置批次时间列表

        print(f"\n轮次 {epoch + 1} 完成:")
        print(f"- 本轮训练时间: {epoch_time:.2f} 秒")
        print(f"- 平均批次时间: {avg_batch_time:.4f} 秒")
        print(f"- 当前损失: {logs.get('loss', 0):.6f}")
        self._print_gpu_stats()

    def on_train_batch_end(self, batch, logs=None):
        current_time = time.time()
        if self.last_batch_time is not None:
            batch_time = current_time - self.last_batch_time
            self.batch_times.append(batch_time)
        self.last_batch_time = current_time

    def _print_gpu_stats(self):
        try:
            gpus = GPUtil.getGPUs()
            for gpu in gpus:
                print(f"\nGPU {gpu.id} 状态:")
                print(f"- 显存使用: {gpu.memoryUsed}/{gpu.memoryTotal} MB ({gpu.memoryUtil * 100:.1f}%)")
                print(f"- GPU利用率: {gpu.load * 100:.1f}%")
                print(f"- 温度: {gpu.temperature}°C")
        except Exception as e:
            print(f"无法获取GPU信息: {str(e)}")

        # 打印系统内存使用情况
        memory = psutil.virtual_memory()
        print(f"\n系统内存使用:")
        print(f"- 已用: {memory.used / 1024 / 1024 / 1024:.1f}GB")
        print(f"- 总量: {memory.total / 1024 / 1024 / 1024:.1f}GB")
        print(f"- 使用率: {memory.percent}%")


class TempFieldMonitor(tf.keras.callbacks.Callback):
    """监控温度场训练过程中的异常值和梯度问题"""

    def __init__(self, model_type='temp'):
        super(TempFieldMonitor, self).__init__()
        self.model_type = model_type
        self.loss_history = []
        self.gradient_norms = []
        self.activation_means = []
        self.activation_stds = []

    def on_epoch_begin(self, epoch, logs=None):
        print(f"\n开始监控{self.model_type}模型第{epoch + 1}轮训练...")

    def on_epoch_end(self, epoch, logs=None):
        # 记录损失
        current_loss = logs.get('loss')
        self.loss_history.append(current_loss)

        # 检测损失异常
        if len(self.loss_history) > 1:
            loss_change = abs(self.loss_history[-1] - self.loss_history[-2])
            if loss_change > 100:  # 损失变化过大
                print(f"\n⚠️ 警告: 检测到损失突变! 变化量: {loss_change:.2f}")
                print("可能存在梯度爆炸或消失问题，建议调整学习率或使用梯度裁剪")
            elif current_loss > 1000:  # 损失值过大
                print(f"\n⚠️ 警告: 损失值异常高! 当前损失: {current_loss:.2f}")
                print("建议检查数据归一化和模型结构")

        # 输出统计信息
        print(f"\n{self.model_type}模型第{epoch + 1}轮监控:")
        print(f"- 当前损失: {current_loss:.6f}")
        if len(self.loss_history) > 5:
            recent_trend = sum(self.loss_history[-5:]) / 5
            print(f"- 最近5轮平均损失: {recent_trend:.6f}")

            # 检测训练停滞
            if all(abs(self.loss_history[-i] - self.loss_history[-i - 1]) < 1e-5 for i in range(1, 5)):
                print("⚠️ 警告: 训练可能已停滞，建议调整学习率或模型结构")

    def on_train_batch_end(self, batch, logs=None):
        # 每10个批次记录一次
        if batch % 10 == 0:
            # 获取当前批次损失
            batch_loss = logs.get('loss')

            # 检测批次内异常
            if batch_loss is not None and batch_loss > 1000:
                print(f"\n⚠️ 批次{batch}损失异常: {batch_loss:.2f}")


# 添加梯度裁剪回调类
class GradientClippingCallback(tf.keras.callbacks.Callback):
    def __init__(self, clipnorm=1.0):
        super(GradientClippingCallback, self).__init__()
        self.clipnorm = clipnorm
        self.enabled = False  # 默认禁用梯度裁剪

    def on_train_begin(self, logs=None):
        # 在训练开始时输出一次信息
        print(f"梯度裁剪功能已禁用，以避免'NoneType' object has no attribute 'dtype'错误")

    def on_epoch_begin(self, epoch, logs=None):
        # 不再尝试设置梯度裁剪
        pass

    # 移除on_train_batch_begin方法，不再尝试设置梯度裁剪


def get_gpu_memory(gpu_device):
    """获取GPU显存大小（单位：MB）"""
    try:
        gpu_details = tf.config.experimental.get_device_details(gpu_device)
        return gpu_details['device_memory_size'] // (1024 * 1024)  # 转换为MB
    except:
        try:
            # 使用GPUtil作为替代方案
            gpus = GPUtil.getGPUs()
            for gpu in gpus:
                if gpu_device.name.endswith(str(gpu.id)):
                    return gpu.memoryTotal
        except:
            pass
    # 如果无法获取确切数值，返回默认值
    return 4096  # 默认4GB


def compare_fields(true_field, pred_field, title, save_dir):
    """生成真实场与预测场的比较图，包括差异分析

    参数:
        true_field: 真实场数据
        pred_field: 预测场数据
        title: 图表标题
        save_dir: 保存目录
    """
    # 确保数据类型兼容
    true_field = true_field.astype(np.float32)
    pred_field = pred_field.astype(np.float32)

    # 确保预测场与真实场形状相同
    m, n = true_field.shape
    try:
        if pred_field.shape != true_field.shape:
            print(f"预测场形状 {pred_field.shape} 与真实场形状 {true_field.shape} 不匹配，尝试调整")
            # 尝试使用插值调整大小
            try:
                from scipy.ndimage import zoom
                zoom_factor = (m / pred_field.shape[0], n / pred_field.shape[1])
                pred_field_reshaped = zoom(pred_field, zoom_factor, order=1)
                print(f"已将预测场调整为与真实场相同形状: {pred_field_reshaped.shape}")
            except Exception as e:
                print(f"插值调整失败: {str(e)}")
                # 使用零填充并将预测场放在中心位置
                pred_field_reshaped = np.zeros_like(true_field)
                start_row = (m - pred_field.shape[0]) // 2
                start_col = (n - pred_field.shape[1]) // 2
                end_row = min(start_row + pred_field.shape[0], m)
                end_col = min(start_col + pred_field.shape[1], n)
                pred_height = min(pred_field.shape[0], m - start_row)
                pred_width = min(pred_field.shape[1], n - start_col)
                pred_field_reshaped[start_row:end_row, start_col:end_col] = pred_field[:pred_height, :pred_width]
        else:
            pred_field_reshaped = pred_field
    except Exception as e:
        print(f"处理预测场形状时出错: {str(e)}")
        # 如果无法处理，尝试使用零填充
        pred_field_reshaped = np.zeros_like(true_field)
        try:
            # 尝试将一维数组重塑为二维
            if len(pred_field.shape) == 1:
                side_len = int(np.sqrt(pred_field.shape[0]))
                if side_len * side_len == pred_field.shape[0]:
                    temp = pred_field.reshape(side_len, side_len)
                    # 使用插值调整大小
                    from scipy.ndimage import zoom
                    zoom_factor = (m / side_len, n / side_len)
                    pred_field_reshaped = zoom(temp, zoom_factor, order=1)
                else:
                    # 如果不是完全平方数，直接填充
                    min_size = min(pred_field.shape[0], m * n)
                    pred_field_reshaped.flat[:min_size] = pred_field[:min_size]
            else:
                # 如果是其他形状，尝试最佳匹配
                min_rows = min(pred_field.shape[0], m)
                min_cols = min(pred_field.shape[1] if len(pred_field.shape) > 1 else 1, n)
                pred_field_reshaped[:min_rows, :min_cols] = pred_field[:min_rows, :min_cols]
        except Exception as e2:
            print(f"尝试重塑预测场时出错: {str(e2)}")
            # 最后手段：使用随机数据
            pred_field_reshaped = np.random.rand(*true_field.shape) * (
                    np.max(true_field) - np.min(true_field)) + np.min(true_field)
            print("警告：使用随机数据替代预测场")

    # 计算误差
    error = true_field - pred_field_reshaped
    abs_error = np.abs(error)

    # 计算统计数据
    mean_error = np.mean(error)
    rmse = np.sqrt(np.mean(np.square(error)))
    max_error = np.max(abs_error)
    min_error = np.min(abs_error)

    # 创建可视化图表
    fig = plt.figure(figsize=(18, 10))

    # 使用相同的色标范围
    vmin = min(np.min(true_field), np.min(pred_field_reshaped))
    vmax = max(np.max(true_field), np.max(pred_field_reshaped))

    # 1. 真实场
    ax1 = fig.add_subplot(2, 2, 1)
    im1 = ax1.imshow(true_field, cmap='jet', vmin=vmin, vmax=vmax)
    ax1.set_title('Ground Truth')
    plt.colorbar(im1, ax=ax1)

    # 2. 预测场
    ax2 = fig.add_subplot(2, 2, 2)
    im2 = ax2.imshow(pred_field_reshaped, cmap='jet', vmin=vmin, vmax=vmax)
    ax2.set_title('Prediction')
    plt.colorbar(im2, ax=ax2)

    # 3. 误差分布
    ax3 = fig.add_subplot(2, 2, 3)
    error_max = max(abs(np.min(error)), abs(np.max(error)))
    im3 = ax3.imshow(error, cmap='coolwarm', vmin=-error_max, vmax=error_max)
    ax3.set_title('Error Distribution (Ground Truth - Prediction)')
    plt.colorbar(im3, ax=ax3)

    # 4. 误差直方图
    ax4 = fig.add_subplot(2, 2, 4)
    ax4.hist(error.flatten(), bins=50, color='skyblue', edgecolor='black', alpha=0.7)
    ax4.axvline(x=0, color='r', linestyle='--')
    ax4.set_title('Error Histogram')
    ax4.set_xlabel('Error Value')
    ax4.set_ylabel('Frequency')

    # 添加统计信息
    stats_text = f"Mean Error: {mean_error:.6f}\nRMSE: {rmse:.6f}\nMax Error: {max_error:.6f}\nMin Error: {min_error:.6f}"
    fig.text(0.02, 0.02, stats_text, fontsize=10, bbox=dict(facecolor='white', alpha=0.8))

    # 设置总标题 - 英文标题映射
    title_map = {
        "相场误差分析": "Phase Field Error Analysis",
        "温度场误差分析": "Temperature Field Error Analysis",
        "溶质场误差分析": "Concentration Field Error Analysis"
    }
    fig.suptitle(title_map.get(title, title), fontsize=16)
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])

    # 保存图像 - 使用英文文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename_map = {
        "相场误差分析": "phase_field_error_analysis",
        "温度场误差分析": "temperature_field_error_analysis",
        "溶质场误差分析": "concentration_field_error_analysis"
    }
    filename = filename_map.get(title, title.replace(' ', '_'))
    save_path = os.path.join(save_dir, f"{filename}_{timestamp}.png")
    plt.savefig(save_path)
    print(f"已保存误差分析图到: {save_path}")
    plt.close()

    # 保存误差统计数据
    stats = {
        "mean_error": float(mean_error),
        "rmse": float(rmse),
        "max_error": float(max_error),
        "min_error": float(min_error)
    }
    stats_path = os.path.join(save_dir, f"{filename}_stats_{timestamp}.json")
    with open(stats_path, 'w') as f:
        json.dump(stats, f, indent=4)
    print(f"已保存误差统计数据到: {stats_path}")

    return stats


def predict_with_original_resolution(model, input_field, downsample_factor=1, norm_params=None):
    """使用原始分辨率进行预测，通过高质量上采样恢复预测结果到原始分辨率

    参数:
        model: 训练好的模型
        input_field: 输入场数据，形状应为原始分辨率
        downsample_factor: 训练时使用的降采样因子
        norm_params: 归一化参数，用于温度场归一化和反归一化

    返回:
        原始分辨率的预测结果
    """
    print(f"开始执行原始分辨率预测，输入场形状: {input_field.shape}")

    # 保存原始形状
    orig_shape = input_field.shape

    # 对输入进行降采样处理以匹配训练数据
    if downsample_factor > 1:
        # 使用双三次插值进行降采样，保留更多细节
        try:
            from scipy.ndimage import zoom
            zoom_factor = 1.0 / downsample_factor
            downsampled = zoom(input_field, zoom_factor, order=3)  # order=3为双三次插值
            print(f"使用双三次插值将输入场降采样为: {downsampled.shape}")
        except Exception as e:
            print(f"双三次插值降采样失败: {str(e)}，使用简单步长降采样")
            downsampled = input_field[::downsample_factor, ::downsample_factor]
            print(f"已将输入场降采样为: {downsampled.shape}")
    else:
        downsampled = input_field

    # 对温度场数据进行归一化处理（如果需要）
    if norm_params is not None:
        print("对温度场输入进行归一化")
        if 'mean' in norm_params and 'std' in norm_params:
            # 使用Z-score归一化
            print("使用Z-score归一化")
            downsampled = (downsampled - norm_params['mean']) / (norm_params['std'] + 1e-10)
        else:
            # 兼容旧版本的Min-Max归一化
            print("使用Min-Max归一化")
            downsampled = (downsampled - norm_params['min']) / (norm_params['max'] - norm_params['min'] + 1e-10)

    # 为模型准备输入
    model_input = downsampled.reshape(1, downsampled.shape[0], downsampled.shape[1], 1)
    print(f"模型输入形状: {model_input.shape}")

    # 使用模型进行预测
    print("执行预测...")
    prediction = model.predict(model_input)[0]
    print(f"初始预测结果形状: {prediction.shape}")

    # 确保预测结果为float32类型，解决float16不兼容问题
    prediction = prediction.astype(np.float32)

    # 重塑预测结果为2D场
    pred_shape = (downsampled.shape[0], downsampled.shape[1])
    try:
        prediction_2d = prediction.reshape(pred_shape)
        print(f"已将预测结果重塑为2D场: {prediction_2d.shape}")
    except Exception as e:
        print(f"重塑预测结果时出错: {str(e)}")
        # 如果无法直接重塑，使用适当的大小处理
        if len(prediction.shape) == 1:
            side_len = int(np.sqrt(prediction.shape[0]))
            if side_len * side_len == prediction.shape[0]:
                prediction_2d = prediction.reshape((side_len, side_len))
            else:
                # 尝试与输入形状匹配
                if prediction.shape[0] == downsampled.shape[0] * downsampled.shape[1]:
                    prediction_2d = prediction.reshape(downsampled.shape)
                else:
                    # 最后手段
                    prediction_2d = np.zeros(pred_shape, dtype=np.float32)
                    min_size = min(prediction.shape[0], np.prod(pred_shape))
                    prediction_2d.flat[:min_size] = prediction[:min_size]
        else:
            prediction_2d = prediction

    # 如果温度场数据进行了归一化，需要反归一化
    if norm_params is not None:
        print("对预测结果进行反归一化")
        if 'mean' in norm_params and 'std' in norm_params:
            # Z-score反归一化
            print("使用Z-score反归一化")
            prediction_2d = prediction_2d * norm_params['std'] + norm_params['mean']

            # 如果有异常值界限，进行裁剪
            if 'lower_bound' in norm_params and 'upper_bound' in norm_params:
                print(f"裁剪异常值: [{norm_params['lower_bound']}, {norm_params['upper_bound']}]")
                prediction_2d = np.clip(prediction_2d, norm_params['lower_bound'], norm_params['upper_bound'])
        else:
            # 兼容旧版本的Min-Max反归一化
            print("使用Min-Max反归一化")
            prediction_2d = prediction_2d * (norm_params['max'] - norm_params['min']) + norm_params['min']

    # 如果使用了降采样，现在需要上采样回原始分辨率
    if downsample_factor > 1:
        print(f"上采样预测结果到原始分辨率: {orig_shape}")
        try:
            from scipy.ndimage import zoom
            # 确保数据类型兼容
            prediction_2d = prediction_2d.astype(np.float32)

            # 使用双三次插值进行上采样
            zoom_factors = (orig_shape[0] / prediction_2d.shape[0],
                            orig_shape[1] / prediction_2d.shape[1])

            # 使用分步上采样，先上采样到接近目标大小，然后精确调整到目标大小
            # 第一步：使用双三次插值进行上采样
            upscaled_prediction = zoom(prediction_2d, zoom_factors, order=3)  # 使用双三次插值提高质量

            # 第二步：如果形状不完全匹配，使用精确调整
            if upscaled_prediction.shape != orig_shape:
                print(f"形状不完全匹配，进行精确调整: {upscaled_prediction.shape} -> {orig_shape}")
                # 使用NumPy进行简单调整
                temp = np.zeros(orig_shape, dtype=np.float32)
                min_rows = min(upscaled_prediction.shape[0], orig_shape[0])
                min_cols = min(upscaled_prediction.shape[1], orig_shape[1])
                temp[:min_rows, :min_cols] = upscaled_prediction[:min_rows, :min_cols]
                upscaled_prediction = temp

            print(f"上采样后形状: {upscaled_prediction.shape}")

            # 应用边界平滑处理，减少上采样伪影
            try:
                from scipy.ndimage import gaussian_filter
                # 仅对边缘区域应用高斯平滑
                edge_mask = np.zeros_like(upscaled_prediction)
                border = 10  # 边界宽度
                edge_mask[:border, :] = 1
                edge_mask[-border:, :] = 1
                edge_mask[:, :border] = 1
                edge_mask[:, -border:] = 1

                # 对边缘应用高斯模糊
                blurred = gaussian_filter(upscaled_prediction, sigma=1)
                # 只在边缘区域使用模糊结果
                upscaled_prediction = np.where(edge_mask == 1, blurred, upscaled_prediction)
                print("已应用边界平滑处理")
            except Exception as e:
                print(f"边界平滑处理失败: {str(e)}")

            return upscaled_prediction

        except Exception as e:
            print(f"上采样过程中出错: {str(e)}")
            # 如果上采样失败，返回调整大小后的数组
            upscaled_prediction = np.zeros(orig_shape, dtype=np.float32)
            # 将预测结果复制到中心位置，而不是左上角
            start_row = (orig_shape[0] - prediction_2d.shape[0]) // 2
            start_col = (orig_shape[1] - prediction_2d.shape[1]) // 2
            upscaled_prediction[start_row:start_row + prediction_2d.shape[0],
            start_col:start_col + prediction_2d.shape[1]] = prediction_2d
            return upscaled_prediction
    else:
        return prediction_2d


def create_coupled_model(phi_shape, temp_shape, solute_shape, output_dims):
    """创建耦合模型，同时处理相场、温度场和溶质场的相互关系

    参数:
        phi_shape: 相场输入形状
        temp_shape: 温度场输入形状
        solute_shape: 溶质场输入形状
        output_dims: 包含三个场输出维度的元组 (phi_dim, temp_dim, solute_dim)

    返回:
        耦合模型
    """
    phi_dim, temp_dim, solute_dim = output_dims

    # 指定在GPU上创建模型
    with tf.device('/GPU:0'):
        # 三个场的输入
        phi_input = tf.keras.layers.Input(shape=phi_shape, name='phi_input')
        temp_input = tf.keras.layers.Input(shape=temp_shape, name='temp_input')
        solute_input = tf.keras.layers.Input(shape=solute_shape, name='solute_input')

        # 相场编码器
        phi_x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same')(phi_input)
        phi_x = tf.keras.layers.BatchNormalization()(phi_x)
        phi_x = tf.keras.layers.MaxPooling2D((2, 2))(phi_x)
        phi_x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same')(phi_x)
        phi_x = tf.keras.layers.BatchNormalization()(phi_x)
        phi_x = tf.keras.layers.GlobalAveragePooling2D()(phi_x)
        phi_features = tf.keras.layers.Dense(128, activation='relu')(phi_x)

        # 温度场编码器
        temp_x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same')(temp_input)
        temp_x = tf.keras.layers.BatchNormalization()(temp_x)
        temp_x = tf.keras.layers.MaxPooling2D((2, 2))(temp_x)
        temp_x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same')(temp_x)
        temp_x = tf.keras.layers.BatchNormalization()(temp_x)
        temp_x = tf.keras.layers.GlobalAveragePooling2D()(temp_x)
        temp_features = tf.keras.layers.Dense(128, activation='relu')(temp_x)

        # 溶质场编码器
        solute_x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same')(solute_input)
        solute_x = tf.keras.layers.BatchNormalization()(solute_x)
        solute_x = tf.keras.layers.MaxPooling2D((2, 2))(solute_x)
        solute_x = tf.keras.layers.Conv2D(64, (3, 3), activation='relu', padding='same')(solute_x)
        solute_x = tf.keras.layers.BatchNormalization()(solute_x)
        solute_x = tf.keras.layers.GlobalAveragePooling2D()(solute_x)
        solute_features = tf.keras.layers.Dense(128, activation='relu')(solute_x)

        # 融合特征
        combined_features = tf.keras.layers.Concatenate()([phi_features, temp_features, solute_features])
        combined_features = tf.keras.layers.Dense(256, activation='relu')(combined_features)
        combined_features = tf.keras.layers.BatchNormalization()(combined_features)
        combined_features = tf.keras.layers.Dropout(0.3)(combined_features)

        # 相场输出头
        phi_combined = tf.keras.layers.Concatenate()([phi_features, combined_features])
        phi_combined = tf.keras.layers.Dense(256, activation='relu')(phi_combined)
        phi_combined = tf.keras.layers.BatchNormalization()(phi_combined)
        phi_output = tf.keras.layers.Dense(phi_dim, name='phi_output')(phi_combined)

        # 温度场输出头
        temp_combined = tf.keras.layers.Concatenate()([temp_features, combined_features])
        temp_combined = tf.keras.layers.Dense(256, activation='relu')(temp_combined)
        temp_combined = tf.keras.layers.BatchNormalization()(temp_combined)
        temp_output = tf.keras.layers.Dense(temp_dim, name='temp_output')(temp_combined)

        # 溶质场输出头
        solute_combined = tf.keras.layers.Concatenate()([solute_features, combined_features])
        solute_combined = tf.keras.layers.Dense(256, activation='relu')(solute_combined)
        solute_combined = tf.keras.layers.BatchNormalization()(solute_combined)
        solute_output = tf.keras.layers.Dense(solute_dim, name='solute_output')(solute_combined)

        # 创建模型
        model = tf.keras.Model(
            inputs=[phi_input, temp_input, solute_input],
            outputs=[phi_output, temp_output, solute_output]
        )

        # 配置优化器
        optimizer = tf.keras.optimizers.Adam(learning_rate=5e-5)
        if tf.config.list_physical_devices('GPU'):
            optimizer = tf.keras.mixed_precision.LossScaleOptimizer(optimizer)

        # 编译模型 - 使用不同的损失函数权重
        model.compile(
            optimizer=optimizer,
            loss={
                'phi_output': 'mse',
                'temp_output': 'mse',
                'solute_output': 'mse'
            },
            loss_weights={
                'phi_output': 1.0,
                'temp_output': 1.0,
                'solute_output': 1.0
            },
            metrics=['mae']
        )

    return model


def train_coupled_model(phi_data, temp_data, solute_data, phi_targets, temp_targets, solute_targets,
                        batch_size=4, epochs=100, callbacks=None, model_path=None):
    """训练耦合模型

    参数:
        phi_data: 相场输入数据
        temp_data: 温度场输入数据
        solute_data: 溶质场输入数据
        phi_targets: 相场目标数据
        temp_targets: 温度场目标数据
        solute_targets: 溶质场目标数据
        batch_size: 批次大小
        epochs: 训练轮数
        callbacks: 回调函数列表
        model_path: 模型保存路径

    返回:
        训练历史
    """
    print("\n开始训练耦合模型...")
    print("=" * 50)

    # 创建耦合模型
    model = create_coupled_model(
        phi_shape=phi_data[0].shape,
        temp_shape=temp_data[0].shape,
        solute_shape=solute_data[0].shape,
        output_dims=(phi_targets[0].shape[0], temp_targets[0].shape[0], solute_targets[0].shape[0])
    )

    print(f"耦合模型输入形状: 相场{phi_data[0].shape}, 温度场{temp_data[0].shape}, 溶质场{solute_data[0].shape}")
    print(
        f"耦合模型输出形状: 相场{phi_targets[0].shape[0]}, 温度场{temp_targets[0].shape[0]}, 溶质场{solute_targets[0].shape[0]}")

    # 训练模型
    training_start = time.time()
    history = model.fit(
        x=[phi_data, temp_data, solute_data],
        y={
            'phi_output': phi_targets,
            'temp_output': temp_targets,
            'solute_output': solute_targets
        },
        batch_size=batch_size,
        epochs=epochs,
        callbacks=callbacks,
        verbose=1
    )

    training_time = time.time() - training_start
    print(f"\n耦合模型训练完成!")
    print(f"总训练时间: {training_time:.2f} 秒")
    print(f"平均每轮时间: {training_time / epochs:.2f} 秒")
    print("=" * 50)

    # 保存模型
    if model_path:
        print(f"保存耦合模型到: {model_path}")
        model.save(model_path)

    return model, history


def generate_multi_step_predictions(model, data_dir, field_type, start_step, end_step, step_interval,
                                    downsample_factor=1, norm_params=None, save_dir=None):
    """生成不同时间步的预测结果对比图

    参数:
        model: 训练好的模型
        data_dir: 数据目录
        field_type: 场类型，如'phase', 'temp', 'solute'
        start_step: 起始步数
        end_step: 结束步数
        step_interval: 步数间隔
        downsample_factor: 降采样因子
        norm_params: 归一化参数（用于温度场）
        save_dir: 保存目录
    """
    print(f"\n生成{field_type}场不同时间步预测结果对比图...")
    print(f"时间步范围: {start_step} 到 {end_step}，间隔: {step_interval}")

    # 确定保存目录
    if save_dir is None:
        save_dir = os.path.dirname(data_dir)

    # 创建保存目录
    if field_type == 'phase':
        result_dir = os.path.join(save_dir, "PhaseField_MultiStep")
        field_name = "相场"
        file_prefix = "相场第"
    elif field_type == 'temp':
        result_dir = os.path.join(save_dir, "TemperatureField_MultiStep")
        field_name = "温度场"
        file_prefix = "温度场第"
    else:  # solute
        result_dir = os.path.join(save_dir, "ConcentrationField_MultiStep")
        field_name = "溶质场"
        file_prefix = "溶质场第"

    os.makedirs(result_dir, exist_ok=True)

    # 确定要加载的时间步
    steps = list(range(start_step, end_step + 1, step_interval))
    if not steps:
        print(f"错误: 指定的时间步范围无效 ({start_step} 到 {end_step}，间隔 {step_interval})")
        return None

    # 加载数据并进行预测
    true_fields = []
    pred_fields = []

    for step in steps:
        try:
            # 构建文件路径
            file_path = os.path.join(data_dir, f"{file_prefix}{step}步.csv")
            if not os.path.exists(file_path):
                print(f"警告: 文件不存在 {file_path}，跳过该时间步")
                continue

            # 加载数据
            field = pd.read_csv(file_path, header=None, encoding='utf-8').values.astype(np.float32)
            print(f"已加载{field_name}第{step}步数据，形状: {field.shape}")

            # 预测
            pred_field = predict_with_original_resolution(
                model,
                field,
                downsample_factor=downsample_factor,
                norm_params=norm_params
            )

            # 保存结果
            true_fields.append((step, field))
            pred_fields.append((step, pred_field))

        except Exception as e:
            print(f"处理第{step}步时出错: {str(e)}")

    if not true_fields or not pred_fields:
        print("未能成功加载和预测任何时间步数据")
        return None

    # 创建多时间步对比图
    n_steps = len(true_fields)
    fig, axes = plt.subplots(2, n_steps, figsize=(n_steps * 4, 8), facecolor='white')

    # 找到所有场的最大最小值，用于统一色标
    all_values = []
    for _, field in true_fields:
        all_values.extend(field.flatten())
    for _, field in pred_fields:
        all_values.extend(field.flatten())

    vmin = np.min(all_values)
    vmax = np.max(all_values)

    # 绘制真实场
    for i, (step, field) in enumerate(true_fields):
        im = axes[0, i].imshow(field, cmap='jet', vmin=vmin, vmax=vmax)
        axes[0, i].set_title(f'Step {step} (Ground Truth)')
        axes[0, i].axis('off')

    # 绘制预测场
    for i, (step, field) in enumerate(pred_fields):
        im = axes[1, i].imshow(field, cmap='jet', vmin=vmin, vmax=vmax)
        axes[1, i].set_title(f'Step {step} (Prediction)')
        axes[1, i].axis('off')

    # 添加色标
    cbar_ax = fig.add_axes([0.92, 0.15, 0.02, 0.7])
    fig.colorbar(im, cax=cbar_ax)

    # 设置总标题
    title_map = {
        'phase': 'Phase Field Multi-Step Predictions',
        'temp': 'Temperature Field Multi-Step Predictions',
        'solute': 'Concentration Field Multi-Step Predictions'
    }
    fig.suptitle(title_map.get(field_type, f'{field_type} Multi-Step Predictions'),
                 fontsize=20, fontweight='bold', y=0.98)

    plt.tight_layout(rect=[0, 0, 0.9, 0.95])

    # 保存图像
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_path = os.path.join(result_dir, f'{field_type}_multi_step_predictions_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"多时间步预测结果对比图已保存为: {save_path}")
    return save_path


def main():
    # 解析命令行参数
    args = parse_args()

    # 设置数据路径
    phi_data_dir = args.phi_data_dir
    temp_data_dir = args.temp_data_dir
    solute_data_dir = args.solute_data_dir
    model_dir = args.model_dir

    # 创建模型保存目录（如果不存在）
    if args.save_models and not os.path.exists(model_dir):
        os.makedirs(model_dir)
        print(f"创建模型保存目录: {model_dir}")

    # 创建结果目录
    results_dir = os.path.join(model_dir, 'results')
    os.makedirs(results_dir, exist_ok=True)
    print(f"创建结果保存目录: {results_dir}")

    # 加载数据
    print("正在加载数据...")
    try:
        phi_data, temp_data, solute_data = load_freeze_data(
            phi_data_dir,
            temp_data_dir,
            solute_data_dir,
            start_step=args.start_step,
            end_step=args.end_step,
            stride=args.stride,
            max_samples=args.max_samples
        )
        print(f"加载完成: {len(phi_data)}个相场, {len(temp_data)}个温度场, {len(solute_data)}个溶质场")

        # 数据加载完成后清理内存
        clear_memory()

    except Exception as e:
        print(f"加载数据失败: {str(e)}")
        return

    # 准备训练数据
    print("正在准备训练数据...")
    try:
        # 计算批次大小 - 根据样本大小和可用内存动态调整
        memory = psutil.virtual_memory()
        available_memory_mb = memory.available / (1024 * 1024)
        sample_size = phi_data[0].nbytes / (1024 * 1024)  # 单个样本估计大小(MB)
        safe_batch_size = max(1, min(args.batch_size, int(available_memory_mb / (sample_size * 10))))
        print(f"动态调整批次大小: {safe_batch_size} (原始设置: {args.batch_size})")
        args.batch_size = safe_batch_size

        # 增大降采样因子以减少内存使用
        effective_downsample = max(args.downsample, 4)  # 确保至少4倍降采样
        print(f"使用 {effective_downsample}x 降采样因子进行训练")

        # 单独处理每个场，减少内存使用
        print("\n准备相场训练数据...")
        X_phi_branch, coords_phi, y_phi, _ = prepare_training_data(
            phi_data, downsample_factor=effective_downsample, field_type='phase',
            use_data_augmentation=args.use_data_augmentation
        )
        # 释放原始相场数据内存
        del phi_data
        clear_memory()

        print("\n准备温度场训练数据...")
        X_temp_branch, coords_temp, y_temp, temp_norm_params = prepare_training_data(
            temp_data, downsample_factor=effective_downsample, field_type='temp',
            use_data_augmentation=args.use_data_augmentation
        )
        # 保存温度场归一化参数，用于后续预测
        if temp_norm_params:
            np.save(os.path.join(model_dir, 'temp_norm_params.npy'), temp_norm_params)
            print(f"温度场归一化参数已保存")
        # 释放原始温度场数据内存
        del temp_data
        clear_memory()

        print("\n准备溶质场训练数据...")
        X_solute_branch, coords_solute, y_solute, _ = prepare_training_data(
            solute_data, downsample_factor=effective_downsample, field_type='solute',
            use_data_augmentation=args.use_data_augmentation
        )
        # 释放原始溶质场数据内存
        del solute_data
        clear_memory()

        print(f"相场分支输入形状: {X_phi_branch.shape}")
        print(f"相场输出形状: {y_phi.shape}")
        print(f"坐标形状: {coords_phi.shape}")
    except Exception as e:
        print(f"准备训练数据失败: {str(e)}")
        return

    # 从命令行参数获取训练参数
    phi_epochs = args.phi_epochs
    temp_epochs = args.temp_epochs
    solute_epochs = args.solute_epochs
    batch_size = args.batch_size

    # 打印训练参数
    print(f"\n训练参数设置:")
    print(f"相场数据目录: {phi_data_dir}")
    print(f"温度场数据目录: {temp_data_dir}")
    print(f"溶质场数据目录: {solute_data_dir}")
    print(f"数据加载范围: 从第{args.start_step}步到第{args.end_step}步，步长{args.stride}")
    print(f"最大样本数量: {args.max_samples}")
    print(f"空间降采样因子: {effective_downsample}")
    print(f"相场训练轮数: {phi_epochs}")
    print(f"温度场训练轮数: {temp_epochs}")
    print(f"浓度场训练轮数: {solute_epochs}")
    print(f"批次大小: {batch_size}")
    print(f"模型保存: {'是' if args.save_models else '否'}")
    print(f"加载已有模型: {'是' if args.load_models else '否'}")
    print(f"使用早停机制: {'是' if args.use_early_stopping else '否'}")
    print(f"使用余弦退火学习率: {'是' if args.use_cosine_decay else '否'}")
    if args.use_early_stopping:
        print(f"早停耐心值: {args.patience}")
    print()

    # 设置回调函数
    callbacks = []
    if args.use_early_stopping:
        early_stopping = tf.keras.callbacks.EarlyStopping(
            monitor='loss',
            patience=args.patience,
            restore_best_weights=True
        )
        callbacks.append(early_stopping)

    # 添加学习率调度器
    if not args.use_cosine_decay:  # 如果不使用余弦退火，则使用ReduceLROnPlateau
        lr_scheduler = tf.keras.callbacks.ReduceLROnPlateau(
            monitor='loss',
            factor=0.5,
            patience=10,
            min_lr=1e-6,
            verbose=1
        )
        callbacks.append(lr_scheduler)

    # 添加GPU监控回调
    gpu_monitor = GPUMonitor()
    callbacks.append(gpu_monitor)

    # 添加模型检查点
    checkpoint_callback = tf.keras.callbacks.ModelCheckpoint(
        os.path.join(model_dir, 'checkpoint_{epoch:02d}_{loss:.4f}.keras'),
        monitor='loss',
        save_best_only=True,
        save_weights_only=False,
        mode='min',
        verbose=1
    )
    callbacks.append(checkpoint_callback)

    # 添加温度场监控回调
    temp_monitor = TempFieldMonitor()
    callbacks.append(temp_monitor)

    # 不再添加梯度裁剪回调
    # 注释掉下面两行代码
    # gradient_clipping = GradientClippingCallback(clipnorm=1.0)
    # callbacks.append(gradient_clipping)

    # 定义模型文件路径
    phi_model_path = os.path.join(model_dir, "phi_model.keras")
    temp_model_path = os.path.join(model_dir, "temp_model.keras")
    solute_model_path = os.path.join(model_dir, "solute_model.keras")
    coupled_model_path = os.path.join(model_dir, "coupled_model.keras")

    # 创建或加载模型
    phi_model = None
    temp_model = None
    solute_model = None
    coupled_model = None

    # 相场模型
    if args.load_models and os.path.exists(phi_model_path):
        print(f"加载已有相场模型: {phi_model_path}")
        phi_model = tf.keras.models.load_model(phi_model_path)
        phi_history = None
    else:
        print(f"\n开始训练相场模型...")
        print("=" * 50)
        training_start = time.time()

        # 使用改进的DeepONetFreeze模型
        phi_model = create_model(
            input_shape=X_phi_branch[0].shape,
            num_points=coords_phi.shape[0],
            output_dim=y_phi[0].shape[0],
            model_type='phase',
            use_cosine_decay=args.use_cosine_decay
        )

        # 创建相场模型特定的回调
        phi_callbacks = callbacks.copy()
        phi_monitor = TempFieldMonitor(model_type='phase')
        phi_callbacks.append(phi_monitor)

        # 记录训练历史
        phi_history = phi_model.fit(
            X_phi_branch, y_phi,
            epochs=phi_epochs,
            batch_size=batch_size,
            callbacks=phi_callbacks,
            verbose=1
        )

        training_time = time.time() - training_start
        print(f"\n相场模型训练完成!")
        print(f"总训练时间: {training_time:.2f} 秒")
        print(f"平均每轮时间: {training_time / phi_epochs:.2f} 秒")
        print("=" * 50)

        if args.save_models:
            print(f"保存相场模型到: {phi_model_path}")
            phi_model.save(phi_model_path)

            # 保存训练历史
            with open(os.path.join(results_dir, 'phi_history.pkl'), 'wb') as f:
                pickle.dump(phi_history.history, f)

    # 清理相场训练数据内存
    if not args.load_models:
        del X_phi_branch, y_phi
        clear_memory()

    # 温度场模型
    if args.load_models and os.path.exists(temp_model_path):
        print(f"加载已有温度场模型: {temp_model_path}")
        temp_model = tf.keras.models.load_model(temp_model_path)
        temp_history = None
    else:
        print(f"\n开始训练温度场模型...")
        print("=" * 50)
        training_start = time.time()

        # 使用改进的DeepONetFreeze模型
        temp_model = create_model(
            input_shape=X_temp_branch[0].shape,
            num_points=coords_temp.shape[0],
            output_dim=y_temp[0].shape[0],
            model_type='temp',
            use_cosine_decay=args.use_cosine_decay
        )

        # 创建温度场模型特定的回调
        temp_callbacks = callbacks.copy()
        temp_monitor = TempFieldMonitor(model_type='temp')
        temp_callbacks.append(temp_monitor)

        # 为温度场模型添加梯度裁剪回调，防止梯度爆炸
        # 注释掉下面两行代码
        # temp_gradient_clipping = GradientClippingCallback(clipnorm=0.5)  # 使用更严格的梯度裁剪
        # temp_callbacks.append(temp_gradient_clipping)

        # 记录训练历史
        temp_history = temp_model.fit(
            X_temp_branch, y_temp,
            epochs=temp_epochs,
            batch_size=batch_size,
            callbacks=temp_callbacks,
            verbose=1
        )

        training_time = time.time() - training_start
        print(f"\n温度场模型训练完成!")
        print(f"总训练时间: {training_time:.2f} 秒")
        print(f"平均每轮时间: {training_time / temp_epochs:.2f} 秒")
        print("=" * 50)

        if args.save_models:
            print(f"保存温度场模型到: {temp_model_path}")
            temp_model.save(temp_model_path)

            # 保存训练历史
            with open(os.path.join(results_dir, 'temp_history.pkl'), 'wb') as f:
                pickle.dump(temp_history.history, f)

    # 清理温度场训练数据内存
    if not args.load_models:
        del X_temp_branch, y_temp
        clear_memory()

    # 浓度场模型
    if args.load_models and os.path.exists(solute_model_path):
        print(f"加载已有浓度场模型: {solute_model_path}")
        solute_model = tf.keras.models.load_model(solute_model_path)
        solute_history = None
    else:
        print(f"\n开始训练浓度场模型...")
        print("=" * 50)
        training_start = time.time()

        # 使用改进的DeepONetFreeze模型
        solute_model = create_model(
            input_shape=X_solute_branch[0].shape,
            num_points=coords_solute.shape[0],
            output_dim=y_solute[0].shape[0],
            model_type='solute',
            use_cosine_decay=args.use_cosine_decay
        )

        # 创建溶质场模型特定的回调
        solute_callbacks = callbacks.copy()
        solute_monitor = TempFieldMonitor(model_type='solute')
        solute_callbacks.append(solute_monitor)

        # 为溶质场模型添加梯度裁剪回调
        # 注释掉下面两行代码
        # solute_gradient_clipping = GradientClippingCallback(clipnorm=1.0)
        # solute_callbacks.append(solute_gradient_clipping)

        # 记录训练历史
        solute_history = solute_model.fit(
            X_solute_branch, y_solute,
            epochs=solute_epochs,
            batch_size=batch_size,
            callbacks=solute_callbacks,
            verbose=1
        )

        training_time = time.time() - training_start
        print(f"\n浓度场模型训练完成!")
        print(f"总训练时间: {training_time:.2f} 秒")
        print(f"平均每轮时间: {training_time / solute_epochs:.2f} 秒")
        print("=" * 50)

        if args.save_models:
            print(f"保存浓度场模型到: {solute_model_path}")
            solute_model.save(solute_model_path)

            # 保存训练历史
            with open(os.path.join(results_dir, 'solute_history.pkl'), 'wb') as f:
                pickle.dump(solute_history.history, f)

    # 清理浓度场训练数据内存
    if not args.load_models:
        del X_solute_branch, y_solute
        clear_memory()

    # 预测并可视化结果
    print("正在进行预测和可视化...")

    # 重新加载一小部分数据用于预测
    try:
        print("加载少量数据用于预测...")
        # 仅加载最后几个时间步的数据用于预测
        pred_phi_data, pred_temp_data, pred_solute_data = load_freeze_data(
            phi_data_dir,
            temp_data_dir,
            solute_data_dir,
            start_step=max(0, args.end_step - 10),  # 只加载最后10个时间步
            end_step=args.end_step,
            stride=1,
            max_samples=10
        )

        print(
            f"预测数据加载完成: 相场形状{pred_phi_data.shape}, 温度场形状{pred_temp_data.shape}, 溶质场形状{pred_solute_data.shape}")

        # 准备预测数据
        try:
            print("准备相场预测数据...")
            X_phi_pred, _, _, _ = prepare_training_data(
                pred_phi_data,
                downsample_factor=args.downsample,
                field_type='phase',
                use_data_augmentation=False  # 预测时不使用数据增强
            )
            print(f"相场预测数据准备完成: 形状{X_phi_pred.shape}")

            print("准备温度场预测数据...")
            # 检查是否有保存好的温度场归一化参数
            temp_norm_path = os.path.join(model_dir, 'temp_norm_params.npy')
            if os.path.exists(temp_norm_path):
                temp_norm_params = np.load(temp_norm_path, allow_pickle=True).item()
                print(f"加载温度场归一化参数: 最小值 {temp_norm_params['min']}, 最大值 {temp_norm_params['max']}")
                # 手动应用归一化
                norm_temp_data = [
                    (field - temp_norm_params['min']) / (temp_norm_params['max'] - temp_norm_params['min'] + 1e-10)
                    for field in pred_temp_data]
                X_temp_pred, _, _, _ = prepare_training_data(
                    norm_temp_data,
                    downsample_factor=args.downsample,
                    field_type='temp',
                    use_data_augmentation=False
                )
            else:
                X_temp_pred, _, _, temp_norm_params = prepare_training_data(
                    pred_temp_data,
                    downsample_factor=args.downsample,
                    field_type='temp',
                    use_data_augmentation=False
                )
            print(f"温度场预测数据准备完成: 形状{X_temp_pred.shape}")

            print("准备溶质场预测数据...")
            X_solute_pred, _, _, _ = prepare_training_data(
                pred_solute_data,
                downsample_factor=args.downsample,
                field_type='solute',
                use_data_augmentation=False
            )
            print(f"溶质场预测数据准备完成: 形状{X_solute_pred.shape}")
        except Exception as e:
            print(f"准备预测数据时出错: {str(e)}")
            raise

        # 预测并可视化结果
        try:
            print("使用原始分辨率进行预测和可视化...")

            # 加载温度场归一化参数（如果存在）
            temp_norm_params = None
            temp_norm_path = os.path.join(model_dir, 'temp_norm_params.npy')
            if os.path.exists(temp_norm_path):
                temp_norm_params = np.load(temp_norm_path, allow_pickle=True).item()
                print(f"加载温度场归一化参数: 最小值 {temp_norm_params['min']}, 最大值 {temp_norm_params['max']}")

            # 相场预测
            print("\n执行相场预测...")
            phi_pred_hires = predict_with_original_resolution(
                phi_model,
                pred_phi_data[-1],
                downsample_factor=args.downsample
            )

            # 温度场预测
            print("\n执行温度场预测...")
            temp_pred_hires = predict_with_original_resolution(
                temp_model,
                pred_temp_data[-1],
                downsample_factor=args.downsample,
                norm_params=temp_norm_params
            )

            # 溶质场预测
            print("\n执行溶质场预测...")
            solute_pred_hires = predict_with_original_resolution(
                solute_model,
                pred_solute_data[-1],
                downsample_factor=args.downsample
            )

            # 可视化预测结果
            print("\n可视化预测结果...")
            visualize_prediction(pred_phi_data[-1], phi_pred_hires, "phase field", phi_data_dir, reshape_pred=False,
                                 save_dir=results_dir)
            visualize_prediction(pred_temp_data[-1], temp_pred_hires, "temperature field", temp_data_dir,
                                 reshape_pred=False, save_dir=results_dir)
            visualize_prediction(pred_solute_data[-1], solute_pred_hires, "concentration field", solute_data_dir,
                                 reshape_pred=False, save_dir=results_dir)

            # 保存预测结果到模型目录
            np.save(os.path.join(results_dir, 'phi_prediction.npy'), phi_pred_hires)
            np.save(os.path.join(results_dir, 'temp_prediction.npy'), temp_pred_hires)
            np.save(os.path.join(results_dir, 'solute_prediction.npy'), solute_pred_hires)
            print(f"预测结果已保存到 {results_dir}")

            # 绘制并保存真实场vs预测场的差值图
            print("生成误差分析图...")
            compare_fields(pred_phi_data[-1], phi_pred_hires, "相场误差分析", results_dir)
            compare_fields(pred_temp_data[-1], temp_pred_hires, "温度场误差分析", results_dir)
            compare_fields(pred_solute_data[-1], solute_pred_hires, "溶质场误差分析", results_dir)
        except Exception as e:
            print(f"可视化预测结果时出错: {str(e)}")
            import traceback
            traceback.print_exc()

        # 清理预测数据
        del pred_phi_data, pred_temp_data, pred_solute_data
        del X_phi_pred, X_temp_pred, X_solute_pred
        clear_memory()

    except Exception as e:
        print(f"预测过程中出错: {str(e)}")
        print("跳过预测和可视化步骤")
        import traceback
        traceback.print_exc()

    # 绘制训练历史
    if not args.load_models:
        plt.figure(figsize=(12, 4))

        plt.subplot(131)
        if phi_history:
            plt.plot(phi_history.history['loss'], label='phase field-loss')
            if 'mae' in phi_history.history:
                plt.plot(phi_history.history['mae'], label='phase field-mae')
            plt.title('phase field-loss')
            plt.xlabel('epochs')
            plt.ylabel('loss')
            plt.legend()

        plt.subplot(132)
        if temp_history:
            plt.plot(temp_history.history['loss'], label='temperature field-loss')
            if 'mae' in temp_history.history:
                plt.plot(temp_history.history['mae'], label='temperature field-mae')
            plt.title('temperature field-loss')
            plt.xlabel('epochs')
            plt.ylabel('loss')
            plt.legend()

        plt.subplot(133)
        if solute_history:
            plt.plot(solute_history.history['loss'], label='concentration field-loss')
            if 'mae' in solute_history.history:
                plt.plot(solute_history.history['mae'], label='concentration field-mae')
            plt.title('concentration field-loss')
            plt.xlabel('epochs')
            plt.ylabel('loss')
            plt.legend()

        plt.tight_layout()
        history_path = os.path.join(results_dir, 'training_history.png')
        plt.savefig(history_path)
        print(f"训练历史已保存为 '{history_path}'")
        plt.close()

    # 如果指定了生成多时间步预测结果对比图
    if args.generate_multi_step:
        print("\n生成多时间步预测结果对比图...")
        try:
            # 加载温度场归一化参数（如果存在）
            temp_norm_params = None
            temp_norm_path = os.path.join(model_dir, 'temp_norm_params.npy')
            if os.path.exists(temp_norm_path):
                temp_norm_params = np.load(temp_norm_path, allow_pickle=True).item()
                print(f"加载温度场归一化参数: 最小值 {temp_norm_params['min']}, 最大值 {temp_norm_params['max']}")

            # 相场多时间步预测
            if phi_model is not None:
                generate_multi_step_predictions(
                    phi_model,
                    phi_data_dir,
                    'phase',
                    args.multi_step_start,
                    args.multi_step_end,
                    args.multi_step_interval,
                    downsample_factor=args.downsample,
                    save_dir=results_dir
                )

            # 温度场多时间步预测
            if temp_model is not None:
                generate_multi_step_predictions(
                    temp_model,
                    temp_data_dir,
                    'temp',
                    args.multi_step_start,
                    args.multi_step_end,
                    args.multi_step_interval,
                    downsample_factor=args.downsample,
                    norm_params=temp_norm_params,
                    save_dir=results_dir
                )

            # 溶质场多时间步预测
            if solute_model is not None:
                generate_multi_step_predictions(
                    solute_model,
                    solute_data_dir,
                    'solute',
                    args.multi_step_start,
                    args.multi_step_end,
                    args.multi_step_interval,
                    downsample_factor=args.downsample,
                    save_dir=results_dir
                )
        except Exception as e:
            print(f"生成多时间步预测结果对比图时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    print("训练和预测完成!")


if __name__ == "__main__":
    # 配置GPU
    gpu_available = setup_gpu()

    # 配置混合精度
    if gpu_available:
        mixed_precision_enabled = setup_mixed_precision()

    # 继续执行主程序
    main()