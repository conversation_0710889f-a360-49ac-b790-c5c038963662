import os

def remove_prefix_one():
    # 获取当前目录下所有文件
    files = os.listdir('.')
    
    # 定义要处理的文件类型
    target_types = ['温度场', '浓度场', '相场']
    
    # 计数器
    count = 0
    
    # 遍历文件列表
    for file in files:
        # 检查是否是目标文件类型以及文件名是否以"1"开头
        if file.startswith('1') and any(type_name in file for type_name in target_types) and file.endswith('.csv'):
            # 创建新文件名,去掉开头的"1"
            new_name = file[1:]
            
            # 重命名文件
            os.rename(file, new_name)
            count += 1
            print(f'将 "{file}" 重命名为 "{new_name}"')
    
    print(f"重命名完成! 共处理了 {count} 个文件。")

if __name__ == "__main__":
    remove_prefix_one() 