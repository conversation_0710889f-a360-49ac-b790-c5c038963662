<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="7">
            <item index="0" class="java.lang.String" itemvalue="tqdm" />
            <item index="1" class="java.lang.String" itemvalue="matplotlib" />
            <item index="2" class="java.lang.String" itemvalue="lxml" />
            <item index="3" class="java.lang.String" itemvalue="torch" />
            <item index="4" class="java.lang.String" itemvalue="torchvision" />
            <item index="5" class="java.lang.String" itemvalue="pycocotools" />
            <item index="6" class="java.lang.String" itemvalue="opencv_python" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyUnresolvedReferencesInspection" enabled="false" level="WARNING" enabled_by_default="false" />
  </profile>
</component>