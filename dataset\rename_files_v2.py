import os
import re

def standardize_filenames():
    # 获取当前目录下所有文件
    files = os.listdir('.')
    
    # 定义要处理的文件类型
    target_types = ['温度场', '浓度场', '相场']
    
    # 遍历文件列表
    for file in files:
        # 检查是否是目标文件类型
        if any(type_name in file for type_name in target_types) and file.endswith('.csv'):
            # 使用正则表达式将"字段第"和"数字步.csv"之间的所有空格去掉
            new_name = re.sub(r'(场第)\s+(\d+步\.csv)', r'\1\2', file)
            
            # 如果文件名有变化，则重命名
            if new_name != file:
                os.rename(file, new_name)
                print(f'将 "{file}" 重命名为 "{new_name}"')

if __name__ == "__main__":
    standardize_filenames()
    print("文件重命名完成！") 