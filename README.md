# 冰晶结晶模拟DeepONet模型训练和预测

本项目使用DeepONet（Deep Operator Network）神经网络模型对FreezeV10生成的冰晶结晶数据进行训练和预测。模型可以通过学习历史时间步的数据来预测未来时间步的场值分布，包括相场、温度场和溶质场。

## 项目结构

本项目包含三个主要的训练脚本：

1. `train_phi_field.py` - 用于训练和预测相场模型
2. `train_temp_field.py` - 用于训练和预测温度场模型
3. `train_solute_field.py` - 用于训练和预测溶质场模型

每个脚本都是独立的，专门针对特定的场进行优化设计。

## 数据结构

数据文件夹应按以下结构组织：

```
- PhaseField_Data/       # 相场数据CSV文件
- PhaseField_Images/     # 相场可视化图像
- TemperatureField_Data/ # 温度场数据CSV文件
- TemperatureField_Images/ # 温度场可视化图像
- ConcentrationField_Data/ # 溶质场数据CSV文件
- ConcentrationField_Images/ # 溶质场可视化图像
- models/               # 模型保存目录
  - phi_model/         # 相场模型
  - temp_model/        # 温度场模型
  - solute_model/      # 溶质场模型
```

## 安装要求

需要安装以下Python依赖：

```bash
pip install -r requirements.txt
```

requirements.txt内容：
```
tensorflow>=2.4.0
numpy>=1.19.2
pandas>=1.2.0
matplotlib>=3.3.3
scipy>=1.6.0
```

## 使用方法

### 相场模型 (train_phi_field.py)

训练相场模型：

```bash
python train_phi_field.py --data_dir ./PhaseField_Data --save_dir ./models/phi_model --num_epochs 50 --batch_size 8 --time_steps 10 --downsample_factor 4 --mode train
```

使用训练好的模型预测：

```bash
python train_phi_field.py --data_dir ./PhaseField_Data --save_dir ./models/phi_model --mode predict
```

### 温度场模型 (train_temp_field.py)

训练温度场模型：

```bash
python train_temp_field.py --data_dir ./TemperatureField_Data --save_dir ./models/temp_model --num_epochs 50 --batch_size 8 --time_steps 10 --downsample_factor 4 --temp_min 235.0 --temp_max 253.0 --mode train
```

使用训练好的模型预测：

```bash
python train_temp_field.py --data_dir ./TemperatureField_Data --save_dir ./models/temp_model --mode predict --temp_min 235.0 --temp_max 253.0
```

### 溶质场模型 (train_solute_field.py)

不使用相场数据训练溶质场模型：

```bash
python train_solute_field.py --data_dir ./ConcentrationField_Data --save_dir ./models/solute_model --num_epochs 50 --batch_size 8 --time_steps 10 --downsample_factor 4 --mode train
```

使用相场数据作为额外输入训练溶质场模型：

```bash
python train_solute_field.py --data_dir ./ConcentrationField_Data --phase_dir ./PhaseField_Data --use_phase --save_dir ./models/solute_model --num_epochs 50 --batch_size 8 --time_steps 10 --downsample_factor 4 --mode train
```

使用训练好的模型预测：

```bash
python train_solute_field.py --data_dir ./ConcentrationField_Data --phase_dir ./PhaseField_Data --use_phase --save_dir ./models/solute_model --mode predict
```

## 参数说明

所有脚本共有的参数：

| 参数 | 描述 | 默认值 |
|------|------|-------|
| `--gpu_id` | 使用的GPU ID | 0 |
| `--no_cuda` | 不使用CUDA (即使可用) | False |
| `--data_dir` | 数据目录路径 | 各脚本不同 |
| `--image_dir` | 图像目录路径，用于加载真实值 | 各脚本不同 |
| `--start_idx` | 数据起始帧索引 | 0 |
| `--end_idx` | 数据结束帧索引 | 3050 |
| `--num_epochs` | 训练轮数 | 1-50 |
| `--batch_size` | 批次大小 | 4 |
| `--save_dir` | 模型保存目录 | 各脚本不同 |
| `--num_workers` | 数据加载的工作进程数 | 4 |
| `--modes` | 傅里叶模式数量 | 12 |
| `--width` | 网络宽度 | 32 |
| `--use_cache` | 使用数据缓存 | True |
| `--learning_rate` | 学习率 | 1e-3 |
| `--sample_stride` | 样本间隔步长 | 1-2 |
| `--time_steps` | 时间步长，预测未来第几帧 | 10 |
| `--weight_decay` | 权重衰减(L2正则化) | 1e-4 |
| `--dropout` | Dropout概率 | 0.0 |
| `--val_split` | 验证集比例 | 0.2 |
| `--mode` | 运行模式 | train |
| `--downsample_factor` | 数据降采样因子 | 4 |

特定脚本的参数：

- 温度场模型特有参数：
  - `--temp_min`: 温度场最小值，用于归一化 (默认: 235.0)
  - `--temp_max`: 温度场最大值，用于归一化 (默认: 253.0)

- 溶质场模型特有参数：
  - `--phase_dir`: 相场数据目录路径 (默认: ./PhaseField_Data)
  - `--use_phase`: 是否使用相场作为额外输入 (默认: False)

## 可视化结果说明

模型预测完成后会生成可视化图像：

- 左侧：原始真实数据 (Ground Truth)
- 中间：模型预测结果 (Model Prediction)
- 右侧：预测误差 (Prediction Error)
   
这些可视化结果保存在各个模型的保存目录中。例如，相场模型的预测结果会保存为`Phase_Field_Prediction_Step{step}.png`。

## 模型性能

模型在训练过程中会生成训练历史图表，展示训练和验证的损失与平均绝对误差(MAE)，保存在模型目录下的`training_history.png`文件中。

## 批处理数据优化

所有脚本都实现了高效的数据处理流程：

1. 采用滑动窗口方法将大型800×800数据分割成小块处理
2. 使用降采样减少数据尺寸，提高训练和预测速度
3. 批量加载和处理数据以减少内存使用
4. GPU内存优化配置

## 注意事项

- 训练时间可能会很长，每轮训练大约需要30秒
- 建议先用较小的数据集和轮数进行测试
- 对于大规模数据集，建议增加`downsample_factor`值以减少内存使用
- 温度场数据需要正确设置`temp_min`和`temp_max`以确保归一化效果
- 溶质场可以选择是否使用相场数据作为额外输入，通常带来更好的预测效果 