"""
@author: jpzxshi
"""
from .module import <PERSON><PERSON><PERSON>
from .module import <PERSON><PERSON><PERSON>
from .module import <PERSON><PERSON><PERSON>
from .fnn import FNN
from .hnn import HNN
from .sympnet import LA<PERSON>ympNet
from .sympnet import <PERSON><PERSON>ympNet
from .seq2seq import S2S
from .deeponet import DeepONet

__all__ = [
    'Module',
    'StructureNN',
    'LossNN',
    'FNN',
    'HNN',
    'LASympNet',
    'GSympNet',
    'S2S',
    'DeepONet',
]


