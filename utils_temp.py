import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt
import os
import pandas as pd
import time
import psutil
import GPUtil
from datetime import datetime
import gc
import json
import pickle

# 配置matplotlib支持中文和美观字体
try:
    # 设置更美观的字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'Arial', 'DejaVu Sans', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    # 设置更美观的图形参数
    plt.rcParams['figure.facecolor'] = 'white'
    plt.rcParams['axes.facecolor'] = 'white'
    plt.rcParams['axes.edgecolor'] = 'black'
    plt.rcParams['axes.linewidth'] = 1.2
    plt.rcParams['axes.labelsize'] = 12
    plt.rcParams['axes.titlesize'] = 14
    plt.rcParams['xtick.labelsize'] = 10
    plt.rcParams['ytick.labelsize'] = 10
    plt.rcParams['legend.fontsize'] = 11
    plt.rcParams['figure.titlesize'] = 16

    print("已配置matplotlib支持中文显示和美观字体")
except Exception as e:
    print(f"配置字体失败: {str(e)}，将使用默认设置")

# 中英文字段映射表，用于替换中文
FIELD_NAME_MAP = {
    "温度场": "Temperature Field",
    "温度场误差分析": "Temperature Field Error Analysis"
}

# ================== 温度场专用工具函数 ==================

def clear_memory():
    gc.collect()
    if tf.config.list_physical_devices('GPU'):
        try:
            tf.keras.backend.clear_session()
        except Exception as e:
            print(f"清理GPU内存时出错: {str(e)}")
    memory = psutil.virtual_memory()
    print(f"内存清理后使用率: {memory.percent}%")
    print(f"可用内存: {memory.available / 1024 / 1024 / 1024:.2f} GB")

def setup_gpu():
    print("TensorFlow版本:", tf.__version__)
    print("\nCUDA配置:")
    print("CUDA是否可用:", tf.test.is_built_with_cuda())
    gpus = tf.config.list_physical_devices('GPU')
    if gpus:
        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print(f"检测到 {len(gpus)} 个GPU设备:")
            for i, gpu in enumerate(gpus):
                print(f"  GPU {i}: {gpu.name}")
            return True
        except RuntimeError as e:
            print(f"GPU配置失败: {str(e)}")
            return False
    else:
        print("未检测到GPU设备，将使用CPU")
        return False

def setup_mixed_precision():
    try:
        policy = tf.keras.mixed_precision.Policy('mixed_float16')
        tf.keras.mixed_precision.set_global_policy(policy)
        print("已启用混合精度训练 (mixed_float16)")
    except Exception as e:
        print(f"混合精度配置失败: {str(e)}")

# ================== 温度场数据加载函数 ==================

def load_temp_data(data_dir, start_step=0, end_step=3050, stride=1, max_samples=None):
    """加载温度场数据 - 支持CSV和NPY格式"""
    print(f"正在从 {data_dir} 加载温度场数据...")
    print(f"参数: start_step={start_step}, end_step={end_step}, stride={stride}, max_samples={max_samples}")

    # 检查数据目录是否存在
    if not os.path.exists(data_dir):
        print(f"错误: 数据目录不存在: {data_dir}")
        # 尝试使用默认的温度场数据目录
        default_dir = "./TemperatureField_Data"
        if os.path.exists(default_dir):
            print(f"使用默认温度场数据目录: {default_dir}")
            data_dir = default_dir
        else:
            return []

    temp_files = []

    # 首先尝试NPY格式
    for step in range(start_step, end_step + 1, stride):
        temp_file = os.path.join(data_dir, f'temp_{step:06d}.npy')
        if os.path.exists(temp_file):
            temp_files.append(temp_file)

    # 如果没有找到NPY文件，尝试CSV格式（中文命名）
    if len(temp_files) == 0:
        print("未找到NPY格式文件，尝试CSV格式...")
        for step in range(start_step, end_step + 1, stride):
            temp_file = os.path.join(data_dir, f'温度场第{step}步.csv')
            if os.path.exists(temp_file):
                temp_files.append(temp_file)

    print(f"找到 {len(temp_files)} 个温度场文件")

    if len(temp_files) == 0:
        print("警告: 没有找到任何温度场文件")
        print("请检查:")
        print(f"1. 数据目录是否正确: {data_dir}")
        print(f"2. 文件格式: NPY格式 (temp_XXXXXX.npy) 或 CSV格式 (温度场第X步.csv)")
        print(f"3. 步数范围是否正确: {start_step} 到 {end_step}")
        return []

    if max_samples and len(temp_files) > max_samples:
        temp_files = temp_files[:max_samples]
        print(f"限制为前 {max_samples} 个文件")

    temp_data = []
    for i, temp_file in enumerate(temp_files):
        try:
            if temp_file.endswith('.npy'):
                # 加载NPY文件
                data = np.load(temp_file)
            elif temp_file.endswith('.csv'):
                # 加载CSV文件
                import pandas as pd
                df = pd.read_csv(temp_file, header=None)
                data = df.values
            else:
                print(f"不支持的文件格式: {temp_file}")
                continue

            temp_data.append(data.astype(np.float32))
            if i < 5:  # 只打印前5个文件的信息
                print(f"  加载文件 {i+1}: {os.path.basename(temp_file)}, 形状: {data.shape}")
        except Exception as e:
            print(f"加载文件失败 {temp_file}: {str(e)}")

    print(f"成功加载 {len(temp_data)} 个温度场文件")

    if len(temp_data) == 0:
        print("错误: 没有成功加载任何温度场数据")

    return temp_data

# ================== 温度场专用归一化函数 ==================

def normalize_temp_data(data, method='zscore'):
    """
    温度场专用归一化函数

    参数:
        data: 输入数据列表
        method: 归一化方法 ('zscore', 'minmax', 'none')

    返回:
        normalized_data: 归一化后的数据
        norm_params: 归一化参数
    """
    if method == 'none':
        return data, None

    # 检查数据是否为空
    if not data or len(data) == 0:
        print("错误: 输入数据为空，无法进行归一化")
        return [], None

    print(f"开始温度场归一化，数据数量: {len(data)}")

    # 计算全局统计量
    all_values = np.concatenate([d.flatten() for d in data])
    
    if method == 'zscore':
        mean_val = np.mean(all_values)
        std_val = np.std(all_values)
        
        print(f"温度场Z-score归一化: 均值={mean_val:.6f}, 标准差={std_val:.6f}")
        
        normalized_data = []
        for d in data:
            normalized = (d - mean_val) / (std_val + 1e-10)
            normalized_data.append(normalized.astype(np.float32))
        
        norm_params = {
            'method': 'zscore',
            'mean': float(mean_val),
            'std': float(std_val)
        }
        
    elif method == 'minmax':
        min_val = np.min(all_values)
        max_val = np.max(all_values)
        
        print(f"温度场MinMax归一化: 最小值={min_val:.6f}, 最大值={max_val:.6f}")
        
        normalized_data = []
        for d in data:
            normalized = (d - min_val) / (max_val - min_val + 1e-10)
            normalized_data.append(normalized.astype(np.float32))
        
        norm_params = {
            'method': 'minmax',
            'min': float(min_val),
            'max': float(max_val)
        }
    
    else:
        raise ValueError(f"不支持的归一化方法: {method}")
    
    return normalized_data, norm_params

def denormalize_temp_data(data, norm_params):
    """
    温度场反归一化函数
    
    参数:
        data: 归一化的数据
        norm_params: 归一化参数
    
    返回:
        denormalized_data: 反归一化后的数据
    """
    if norm_params is None:
        return data
    
    method = norm_params.get('method', 'zscore')
    
    if method == 'zscore':
        mean_val = norm_params['mean']
        std_val = norm_params['std']
        return data * std_val + mean_val
    
    elif method == 'minmax':
        min_val = norm_params['min']
        max_val = norm_params['max']
        return data * (max_val - min_val) + min_val
    
    else:
        print(f"警告: 未知的反归一化方法 {method}")
        return data

# ================== 温度场数据准备函数 ==================

def prepare_temp_training_data(temp_data, downsample_factor=1, use_data_augmentation=True):
    """
    准备温度场训练数据
    
    参数:
        temp_data: 温度场数据列表
        downsample_factor: 降采样因子
        use_data_augmentation: 是否使用数据增强
    
    返回:
        X_train: 输入数据
        coords: 坐标数据
        y_train: 目标数据
        norm_params: 归一化参数
    """
    print(f"准备温度场训练数据，降采样因子: {downsample_factor}")
    
    # 归一化数据
    normalized_data, norm_params = normalize_temp_data(temp_data, method='zscore')
    
    # 准备输入输出对
    X_data = []
    y_data = []
    
    for i in range(len(normalized_data) - 1):
        input_field = normalized_data[i]
        target_field = normalized_data[i + 1]
        
        # 降采样
        if downsample_factor > 1:
            try:
                from scipy.ndimage import zoom
                zoom_factor = 1.0 / downsample_factor
                input_downsampled = zoom(input_field, zoom_factor, order=3)
                target_downsampled = zoom(target_field, zoom_factor, order=3)
            except Exception as e:
                print(f"降采样失败: {str(e)}，使用简单步长降采样")
                input_downsampled = input_field[::downsample_factor, ::downsample_factor]
                target_downsampled = target_field[::downsample_factor, ::downsample_factor]
        else:
            input_downsampled = input_field
            target_downsampled = target_field
        
        X_data.append(input_downsampled)
        y_data.append(target_downsampled)
    
    # 转换为numpy数组
    X_train = np.array(X_data)
    y_train = np.array(y_data)
    
    # 重塑为模型输入格式
    X_train = X_train.reshape(X_train.shape[0], X_train.shape[1], X_train.shape[2], 1)
    y_train = y_train.reshape(y_train.shape[0], -1)
    
    # 生成坐标网格
    h, w = X_train.shape[1], X_train.shape[2]
    x = np.linspace(0, 1, w)
    y = np.linspace(0, 1, h)
    X_coord, Y_coord = np.meshgrid(x, y)
    coords = np.stack([X_coord, Y_coord], axis=-1)
    coords = coords.reshape(-1, 2)
    
    print(f"训练数据准备完成:")
    print(f"  输入形状: {X_train.shape}")
    print(f"  输出形状: {y_train.shape}")
    print(f"  坐标形状: {coords.shape}")
    
    return X_train, coords, y_train, norm_params

# ================== 温度场专用预测函数 ==================

def predict_temp_with_original_resolution(model, input_field, downsample_factor=1, norm_params=None):
    """温度场专用预测函数，使用原始分辨率"""
    try:
        print(f"开始温度场预测，输入形状: {input_field.shape}")
        
        # 归一化输入
        if norm_params is not None:
            if norm_params['method'] == 'zscore':
                normalized_input = (input_field - norm_params['mean']) / (norm_params['std'] + 1e-10)
            elif norm_params['method'] == 'minmax':
                normalized_input = (input_field - norm_params['min']) / (norm_params['max'] - norm_params['min'] + 1e-10)
            else:
                normalized_input = input_field
        else:
            normalized_input = input_field
        
        # 降采样
        if downsample_factor > 1:
            try:
                from scipy.ndimage import zoom
                zoom_factor = 1.0 / downsample_factor
                downsampled_input = zoom(normalized_input, zoom_factor, order=3)
            except Exception as e:
                print(f"降采样失败: {str(e)}，使用简单步长降采样")
                downsampled_input = normalized_input[::downsample_factor, ::downsample_factor]
        else:
            downsampled_input = normalized_input
        
        # 重塑为模型输入格式
        model_input = downsampled_input.reshape(1, downsampled_input.shape[0], downsampled_input.shape[1], 1)
        
        # 预测
        prediction = model.predict(model_input, verbose=0)
        
        # 重塑预测结果
        pred_shape = downsampled_input.shape
        prediction_reshaped = prediction.reshape(pred_shape)
        
        # 上采样回原始分辨率
        if downsample_factor > 1:
            try:
                from scipy.ndimage import zoom
                zoom_factor = downsample_factor
                prediction_upsampled = zoom(prediction_reshaped.astype(np.float64), zoom_factor, order=1)
                
                # 确保输出尺寸与输入一致
                if prediction_upsampled.shape != input_field.shape:
                    zoom_factor_x = input_field.shape[0] / prediction_upsampled.shape[0]
                    zoom_factor_y = input_field.shape[1] / prediction_upsampled.shape[1]
                    prediction_upsampled = zoom(prediction_upsampled, (zoom_factor_x, zoom_factor_y), order=1)
                
                prediction_upsampled = prediction_upsampled.astype(np.float32)
            except Exception as e:
                print(f"上采样失败: {str(e)}")
                prediction_upsampled = prediction_reshaped
        else:
            prediction_upsampled = prediction_reshaped
        
        # 反归一化
        if norm_params is not None:
            final_prediction = denormalize_temp_data(prediction_upsampled, norm_params)
        else:
            final_prediction = prediction_upsampled
        
        print(f"温度场预测完成，输出形状: {final_prediction.shape}")
        return final_prediction

    except Exception as e:
        print(f"温度场预测失败: {str(e)}")
        return None

# ================== 温度场专用多步预测函数 ==================

def generate_temp_multi_step_predictions(model, data_dir, start_step, end_step, step_interval,
                                        downsample_factor=1, norm_params=None, save_dir=None):
    """生成温度场多时间步预测结果对比图"""
    try:
        print(f"开始生成温度场多时间步预测...")
        print(f"参数: start_step={start_step}, end_step={end_step}, step_interval={step_interval}")
        print(f"降采样因子: {downsample_factor}")
        if norm_params:
            print(f"归一化参数: {norm_params}")

        # 生成预测步数列表
        prediction_steps = list(range(start_step, end_step + 1, step_interval))
        print(f"将预测以下时间步: {prediction_steps}")

        # 创建保存目录
        if save_dir is None:
            save_dir = os.path.dirname(data_dir)
        multi_step_dir = os.path.join(save_dir, "Temperature_Field_MultiStep_Predictions")
        os.makedirs(multi_step_dir, exist_ok=True)

        predictions = []
        ground_truths = []

        for step in prediction_steps:
            try:
                print(f"\n处理第{step}步...")

                # 使用温度场专用的数据加载函数
                data = load_temp_data(data_dir, start_step=step, end_step=step, stride=1, max_samples=1)
                if len(data) == 0:
                    print(f"警告: 无法加载第{step}步数据")
                    continue

                original_data = data[0]
                print(f"原始数据范围: [{original_data.min():.2f}, {original_data.max():.2f}], 均值: {original_data.mean():.2f}")

                # 使用温度场专用的预测函数
                prediction = predict_temp_with_original_resolution(
                    model, original_data, downsample_factor=downsample_factor, norm_params=norm_params
                )

                if prediction is None:
                    print(f"第{step}步预测失败")
                    continue

                print(f"预测结果范围: [{prediction.min():.2f}, {prediction.max():.2f}], 均值: {prediction.mean():.2f}")

                # 为了确保Ground Truth和预测结果在同一数值空间，
                # 对Ground Truth也应用相同的归一化→反归一化流程
                ground_truth = original_data.copy()
                if norm_params is not None:
                    if norm_params.get('method') == 'zscore' and 'mean' in norm_params and 'std' in norm_params:
                        # 归一化然后立即反归一化，确保数值范围一致
                        normalized_gt = (ground_truth - norm_params['mean']) / (norm_params['std'] + 1e-10)
                        ground_truth = normalized_gt * norm_params['std'] + norm_params['mean']
                        print(f"Ground Truth经过归一化处理后范围: [{ground_truth.min():.2f}, {ground_truth.max():.2f}]")
                    elif norm_params.get('method') == 'minmax' and 'min' in norm_params and 'max' in norm_params:
                        # MinMax归一化然后立即反归一化
                        normalized_gt = (ground_truth - norm_params['min']) / (norm_params['max'] - norm_params['min'] + 1e-10)
                        ground_truth = normalized_gt * (norm_params['max'] - norm_params['min']) + norm_params['min']
                        print(f"Ground Truth经过归一化处理后范围: [{ground_truth.min():.2f}, {ground_truth.max():.2f}]")

                predictions.append(prediction)
                ground_truths.append(ground_truth)

                # 保存单个预测结果
                single_save_path = visualize_temp_prediction(
                    ground_truth, prediction, data_dir, save_dir=multi_step_dir
                )
                print(f"第{step}步预测完成，保存到: {single_save_path}")

            except Exception as e:
                print(f"处理第{step}步时出错: {str(e)}")
                import traceback
                traceback.print_exc()
                continue

        # 生成综合对比图
        if len(predictions) > 0:
            try:
                import matplotlib.pyplot as plt

                fig, axes = plt.subplots(2, len(predictions), figsize=(4*len(predictions), 8))
                if len(predictions) == 1:
                    axes = axes.reshape(2, 1)

                for i, (gt, pred, step) in enumerate(zip(ground_truths, predictions, prediction_steps[:len(predictions)])):
                    # Ground Truth
                    im1 = axes[0, i].imshow(gt, cmap='jet', aspect='auto')
                    axes[0, i].set_title(f'Ground Truth\nStep {step}', fontsize=10)
                    axes[0, i].axis('off')
                    plt.colorbar(im1, ax=axes[0, i], shrink=0.8)

                    # Prediction
                    im2 = axes[1, i].imshow(pred, cmap='jet', aspect='auto')
                    axes[1, i].set_title(f'Prediction\nStep {step}', fontsize=10)
                    axes[1, i].axis('off')
                    plt.colorbar(im2, ax=axes[1, i], shrink=0.8)

                plt.suptitle('Temperature Field Multi-Step Predictions', fontsize=14, y=0.95)
                plt.tight_layout()

                # 保存综合对比图
                summary_path = os.path.join(multi_step_dir, f'Temperature_Field_multi_step_summary_{start_step}_{end_step}.png')
                plt.savefig(summary_path, dpi=300, bbox_inches='tight')
                plt.close()
                print(f"✅ 综合对比图已保存到: {summary_path}")

            except Exception as e:
                print(f"生成综合对比图时出错: {str(e)}")

        print(f"\n🎉 温度场多时间步预测完成！共处理了 {len(predictions)} 个时间步")
        return multi_step_dir

    except Exception as e:
        print(f"温度场多时间步预测失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

# ================== 温度场专用可视化函数 ==================

def visualize_temp_prediction(true_field, pred_field, data_dir, save_dir='./results'):
    """温度场专用可视化函数 - 修复Ground Truth显示问题"""
    try:
        # 创建保存目录
        os.makedirs(save_dir, exist_ok=True)
        temp_images_dir = os.path.join(save_dir, 'TempField_Images')
        os.makedirs(temp_images_dir, exist_ok=True)

        # 数据验证和调试信息
        print(f"🔍 可视化数据检查:")
        print(f"   Ground Truth: 形状={true_field.shape}, 范围=[{np.min(true_field):.3f}, {np.max(true_field):.3f}], 均值={np.mean(true_field):.3f}")
        print(f"   Prediction:   形状={pred_field.shape}, 范围=[{np.min(pred_field):.3f}, {np.max(pred_field):.3f}], 均值={np.mean(pred_field):.3f}")

        # 确保数据类型一致
        true_field = true_field.astype(np.float32)
        pred_field = pred_field.astype(np.float32)

        # 创建图形 - 使用与test_improved.py相同的设置
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))
        fig.patch.set_facecolor('white')

        # 🔧 修改为与多时间步预测一致：每个图使用自己的颜色范围
        print(f"   Ground Truth范围: [{np.min(true_field):.3f}, {np.max(true_field):.3f}]")
        print(f"   Prediction范围: [{np.min(pred_field):.3f}, {np.max(pred_field):.3f}]")
        print(f"   🎨 使用独立颜色范围，与多时间步预测保持一致")

        # 使用jet颜色映射 - 与其他可视化保持一致
        temp_cmap = 'jet'  # 蓝色->青色->绿色->黄色->红色

        # Ground Truth - 使用自己的颜色范围（与多时间步预测一致）
        im1 = axes[0].imshow(true_field, cmap=temp_cmap, aspect='auto')
        axes[0].set_title(f'Ground Truth\n范围: [{np.min(true_field):.1f}, {np.max(true_field):.1f}]', fontsize=14, fontweight='bold')
        axes[0].set_xlabel('X', fontsize=12)
        axes[0].set_ylabel('Y', fontsize=12)
        cbar1 = plt.colorbar(im1, ax=axes[0], shrink=0.8)
        cbar1.ax.tick_params(labelsize=10)

        # Prediction - 使用自己的颜色范围（与多时间步预测一致）
        im2 = axes[1].imshow(pred_field, cmap=temp_cmap, aspect='auto')
        axes[1].set_title(f'Prediction\n范围: [{np.min(pred_field):.1f}, {np.max(pred_field):.1f}]', fontsize=14, fontweight='bold')
        axes[1].set_xlabel('X', fontsize=12)
        axes[1].set_ylabel('Y', fontsize=12)
        cbar2 = plt.colorbar(im2, ax=axes[1], shrink=0.8)
        cbar2.ax.tick_params(labelsize=10)

        # Error - 使用更清晰的误差显示
        error = np.abs(true_field - pred_field)
        im3 = axes[2].imshow(error, cmap='hot', interpolation='bilinear')
        axes[2].set_title(f'Absolute Error\n最大: {np.max(error):.3f}, 均值: {np.mean(error):.3f}', fontsize=14, fontweight='bold')
        axes[2].set_xlabel('X', fontsize=12)
        axes[2].set_ylabel('Y', fontsize=12)
        cbar3 = plt.colorbar(im3, ax=axes[2], shrink=0.8)
        cbar3.ax.tick_params(labelsize=10)

        # 添加整体标题
        fig.suptitle('Temperature Field Prediction Results', fontsize=16, fontweight='bold')

        plt.tight_layout()

        # 保存图像
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = os.path.join(temp_images_dir, f'temp_field_prediction_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        plt.close()

        print(f"✅ 温度场预测可视化已保存到: {save_path}")
        return save_path

    except Exception as e:
        print(f"❌ 温度场可视化失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def compare_temp_fields(true_field, pred_field, title, save_dir='./results'):
    """温度场误差分析 - 与相场和溶质场格式保持一致"""
    try:
        # 确保数据类型一致
        true_field = true_field.astype(np.float32)
        pred_field = pred_field.astype(np.float32)

        # 确保预测场与真实场形状相同
        m, n = true_field.shape
        if pred_field.shape != true_field.shape:
            print(f"预测场形状 {pred_field.shape} 与真实场形状 {true_field.shape} 不匹配，尝试调整")
            try:
                from scipy.ndimage import zoom
                zoom_factor = (m / pred_field.shape[0], n / pred_field.shape[1])
                pred_field_reshaped = zoom(pred_field, zoom_factor, order=1)
                print(f"已将预测场调整为与真实场相同形状: {pred_field_reshaped.shape}")
            except Exception as e:
                print(f"插值调整失败: {str(e)}")
                pred_field_reshaped = np.zeros_like(true_field)
                start_row = (m - pred_field.shape[0]) // 2
                start_col = (n - pred_field.shape[1]) // 2
                end_row = min(start_row + pred_field.shape[0], m)
                end_col = min(start_col + pred_field.shape[1], n)
                pred_height = min(pred_field.shape[0], m - start_row)
                pred_width = min(pred_field.shape[1], n - start_col)
                pred_field_reshaped[start_row:end_row, start_col:end_col] = pred_field[:pred_height, :pred_width]
        else:
            pred_field_reshaped = pred_field

        # 计算误差
        error = true_field - pred_field_reshaped
        abs_error = np.abs(error)
        mean_error = np.mean(error)
        rmse = np.sqrt(np.mean(np.square(error)))
        max_error = np.max(abs_error)
        min_error = np.min(abs_error)

        print(f"\n{title}:")
        print(f"  Mean Error: {mean_error:.6f}")
        print(f"  RMSE: {rmse:.6f}")
        print(f"  Max Error: {max_error:.6f}")
        print(f"  Min Error: {min_error:.6f}")

        # 创建可视化图表 - 完全按照test_improved.py的风格
        fig = plt.figure(figsize=(18, 10))
        fig.patch.set_facecolor('white')

        # 🔧 修改为与多时间步预测一致：每个图使用自己的颜色范围
        print(f"🎨 误差分析图使用独立颜色范围，与多时间步预测保持一致")

        # 1. 真实场 - 使用自己的颜色范围
        ax1 = fig.add_subplot(2, 2, 1)
        im1 = ax1.imshow(true_field, cmap='jet')
        ax1.set_title('Ground Truth')
        plt.colorbar(im1, ax=ax1)

        # 2. 预测场 - 使用自己的颜色范围
        ax2 = fig.add_subplot(2, 2, 2)
        im2 = ax2.imshow(pred_field_reshaped, cmap='jet')
        ax2.set_title('Prediction')
        plt.colorbar(im2, ax=ax2)

        # 3. 误差分布
        ax3 = fig.add_subplot(2, 2, 3)
        error_max = max(abs(np.min(error)), abs(np.max(error)))
        im3 = ax3.imshow(error, cmap='coolwarm', vmin=-error_max, vmax=error_max)
        ax3.set_title('Error Distribution (Ground Truth - Prediction)')
        plt.colorbar(im3, ax=ax3)

        # 4. 误差直方图
        ax4 = fig.add_subplot(2, 2, 4)
        ax4.hist(error.flatten(), bins=50, color='skyblue', edgecolor='black', alpha=0.7)
        ax4.axvline(x=0, color='r', linestyle='--')
        ax4.set_title('Error Histogram')
        ax4.set_xlabel('Error Value')
        ax4.set_ylabel('Frequency')

        # 添加统计信息
        stats_text = f"Mean Error: {mean_error:.6f}\nRMSE: {rmse:.6f}\nMax Error: {max_error:.6f}\nMin Error: {min_error:.6f}"
        fig.text(0.02, 0.02, stats_text, fontsize=10, bbox=dict(facecolor='white', alpha=0.8))

        # 设置总标题
        title_map = {
            "相场误差分析": "Phase Field Error Analysis",
            "温度场误差分析": "Temperature Field Error Analysis",
            "溶质场误差分析": "Concentration Field Error Analysis"
        }
        fig.suptitle(title_map.get(title, title), fontsize=16)
        plt.tight_layout(rect=[0, 0.03, 1, 0.95])

        # 保存图像
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        save_path = os.path.join(save_dir, f'temp_error_analysis_{timestamp}.png')
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()

        print(f"温度场误差分析图已保存到: {save_path}")
        return save_path

    except Exception as e:
        print(f"温度场误差分析失败: {str(e)}")
        return None

# ================== 温度场监控类 ==================

class TempFieldMonitor(tf.keras.callbacks.Callback):
    """温度场训练监控类 - 继承自Keras回调"""

    def __init__(self, model_type='temp', enabled=True):
        super().__init__()
        self.model_type = model_type
        self.enabled = enabled
        self.training_history = {
            'loss': [],
            'mae': [],
            'epoch_times': [],
            'memory_usage': []
        }

        if self.enabled:
            print(f"温度场监控已启用 - 模型类型: {model_type}")

    def on_epoch_end(self, epoch, logs=None):
        """训练轮次结束时的回调"""
        if not self.enabled or logs is None:
            return

        # 记录训练指标
        self.training_history['loss'].append(logs.get('loss', 0))
        self.training_history['mae'].append(logs.get('mae', 0))

        # 记录内存使用
        memory = psutil.virtual_memory()
        self.training_history['memory_usage'].append(memory.percent)

        # 打印进度
        if epoch % 10 == 0:
            print(f"Epoch {epoch}: Loss={logs.get('loss', 0):.6f}, MAE={logs.get('mae', 0):.6f}, Memory={memory.percent:.1f}%")

    def save_training_history(self, save_dir='./results'):
        """保存训练历史"""
        if not self.enabled:
            return

        try:
            # 创建训练历史图
            fig, axes = plt.subplots(2, 2, figsize=(15, 10))

            # 损失曲线
            axes[0, 0].plot(self.training_history['loss'], 'b-', linewidth=2)
            axes[0, 0].set_title('Training Loss', fontsize=14)
            axes[0, 0].set_xlabel('Epoch')
            axes[0, 0].set_ylabel('Loss')
            axes[0, 0].grid(True, alpha=0.3)

            # MAE曲线
            axes[0, 1].plot(self.training_history['mae'], 'r-', linewidth=2)
            axes[0, 1].set_title('Training MAE', fontsize=14)
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('MAE')
            axes[0, 1].grid(True, alpha=0.3)

            # 内存使用
            axes[1, 0].plot(self.training_history['memory_usage'], 'g-', linewidth=2)
            axes[1, 0].set_title('Memory Usage', fontsize=14)
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Memory (%)')
            axes[1, 0].grid(True, alpha=0.3)

            # 训练总结
            final_loss = self.training_history['loss'][-1] if self.training_history['loss'] else 0
            final_mae = self.training_history['mae'][-1] if self.training_history['mae'] else 0
            avg_memory = np.mean(self.training_history['memory_usage']) if self.training_history['memory_usage'] else 0

            summary_text = f'Final Loss: {final_loss:.6f}\nFinal MAE: {final_mae:.6f}\nAvg Memory: {avg_memory:.1f}%'
            axes[1, 1].text(0.1, 0.5, summary_text, fontsize=12, verticalalignment='center')
            axes[1, 1].axis('off')

            plt.tight_layout()

            # 保存图像
            save_path = os.path.join(save_dir, 'temp_training_history.png')
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            plt.close()

            print(f"温度场训练历史已保存到: {save_path}")

        except Exception as e:
            print(f"保存温度场训练历史失败: {str(e)}")

# ================== GPU监控类 ==================

class GPUMonitor(tf.keras.callbacks.Callback):
    """GPU监控类 - 继承自Keras回调"""

    def __init__(self, enabled=True):
        super().__init__()
        self.enabled = enabled
        try:
            self.gpu_available = len(GPUtil.getGPUs()) > 0
        except:
            self.gpu_available = False

        if self.enabled and self.gpu_available:
            print("GPU监控已启用")
        elif self.enabled:
            print("未检测到GPU，GPU监控已禁用")

    def get_gpu_info(self):
        """获取GPU信息"""
        if not self.enabled or not self.gpu_available:
            return None

        try:
            gpus = GPUtil.getGPUs()
            if gpus:
                gpu = gpus[0]  # 使用第一个GPU
                return {
                    'memory_used': gpu.memoryUsed,
                    'memory_total': gpu.memoryTotal,
                    'memory_percent': gpu.memoryUtil * 100,
                    'temperature': gpu.temperature,
                    'load': gpu.load * 100
                }
        except Exception as e:
            print(f"获取GPU信息失败: {str(e)}")

        return None

    def print_gpu_status(self):
        """打印GPU状态"""
        gpu_info = self.get_gpu_info()
        if gpu_info:
            print(f"GPU状态: 内存使用 {gpu_info['memory_percent']:.1f}%, "
                  f"负载 {gpu_info['load']:.1f}%, 温度 {gpu_info['temperature']}°C")

    def on_epoch_end(self, epoch, logs=None):
        """每个epoch结束时的回调"""
        if self.enabled and epoch % 5 == 0:  # 每5个epoch打印一次
            self.print_gpu_status()
