import numpy as np
import matplotlib.pyplot as plt
from scipy.sparse import diags
from scipy.sparse.linalg import spsolve
from tqdm import tqdm
import time
from scipy import sparse

class CHSolver:
    """二维Cahn-Hilliard方程求解器
    
    方程形式: ∂u/∂t = ∇²(-ε²∇²u + f(u))
    其中 f(u) = u³ - u
    """
    
    def __init__(self, nx=128, ny=128, Lx=1.0, Ly=1.0, dt=1e-5, epsilon=0.01):
        """
        初始化求解器参数
        
        参数:
            nx, ny: x和y方向的网格点数
            Lx, Ly: x和y方向的域长度
            dt: 时间步长
            epsilon: 界面宽度参数
        """
        self.nx = nx
        self.ny = ny
        self.Lx = Lx
        self.Ly = Ly
        self.dt = dt
        self.epsilon = epsilon
        
        # 网格尺寸
        self.dx = Lx / (nx - 1)
        self.dy = Ly / (ny - 1)
        
        # 构建拉普拉斯算子
        self._build_laplacian()
    
    def _build_laplacian(self):
        """构建二维拉普拉斯有限差分矩阵"""
        nx, ny = self.nx, self.ny
        dx2, dy2 = self.dx**2, self.dy**2
        
        # 构建一维拉普拉斯算子
        main_diag = -2.0 * np.ones(nx)
        off_diag = np.ones(nx-1)
        
        Dx = diags([main_diag, off_diag, off_diag], [0, 1, -1], shape=(nx, nx))
        Dx = Dx / dx2
        
        main_diag = -2.0 * np.ones(ny)
        off_diag = np.ones(ny-1)
        
        Dy = diags([main_diag, off_diag, off_diag], [0, 1, -1], shape=(ny, ny))
        Dy = Dy / dy2
        
        # 构建二维拉普拉斯算子
        Ix = np.eye(nx)
        Iy = np.eye(ny)
        
        self.lap = (sparse.kron(Dx, Iy, format="csr") + sparse.kron(Ix, Dy, format="csr")).tocsr()
        
        # 隐式步长矩阵
        self.M = sparse.eye(nx*ny) - self.dt*(<EMAIL>)*self.epsilon**2
    
    def f(self, u):
        """非线性项：f(u) = u³ - u，加入值范围限制"""
        # 首先裁剪u的值，防止溢出
        u_clipped = np.clip(u, -10.0, 10.0)
        return u_clipped**3 - u_clipped
    
    def df(self, u):
        """f(u)的导数：f'(u) = 3u² - 1，加入值范围限制"""
        # 首先裁剪u的值，防止溢出
        u_clipped = np.clip(u, -10.0, 10.0)
        return 3*u_clipped**2 - 1
    
    def solve(self, u0, n_steps, save_interval=10):
        """求解CH方程
        
        参数:
            u0: 初始条件
            n_steps: 时间步数
            save_interval: 每隔多少步保存一次结果
            
        返回:
            solutions: 保存的解列表
            times: 对应的时间点
        """
        u = u0.copy().flatten()
        solutions = [u0.copy()]
        times = [0]
        
        for step in tqdm(range(1, n_steps+1)):
            # 构建非线性项
            f_u = self.f(u)
            df_u = self.df(u)
            
            # 隐式-显式方法
            # 隐式处理线性项，显式处理非线性项
            b = u + self.dt * self.lap @ (f_u)
            u = spsolve(self.M, b)
            
            if step % save_interval == 0:
                solutions.append(u.reshape(self.nx, self.ny))
                times.append(step * self.dt)
        
        return solutions, times
    
    def generate_random_initial(self, n_samples, noise_scale=0.1):
        """生成随机初始条件
        
        参数:
            n_samples: 要生成的样本数
            noise_scale: 噪声尺度
            
        返回:
            初始条件数组 shape = (n_samples, nx, ny)
        """
        init_conditions = []
        
        for _ in range(n_samples):
            # 随机初始条件: 噪声加到正弦波或随机形状上
            base = np.zeros((self.nx, self.ny))
            
            # 添加随机的正弦波
            kx = np.random.randint(1, 5)
            ky = np.random.randint(1, 5)
            amp = np.random.uniform(0.3, 0.7)
            
            x = np.linspace(0, self.Lx, self.nx)
            y = np.linspace(0, self.Ly, self.ny)
            X, Y = np.meshgrid(x, y, indexing='ij')
            
            base += amp * np.sin(kx * np.pi * X / self.Lx) * np.sin(ky * np.pi * Y / self.Ly)
            
            # 添加噪声
            noise = np.random.normal(0, noise_scale, (self.nx, self.ny))
            
            init_condition = base + noise
            # 确保值域在 [-1, 1] 之间
            init_condition = np.clip(init_condition, -1, 1)
            
            init_conditions.append(init_condition)
        
        return np.array(init_conditions)
    
    def generate_dataset(self, n_samples, n_steps, save_interval=10):
        """生成用于训练DeepONet的数据集"""
        print(f"生成 {n_samples} 个CH方程样本...")
        
        # 生成随机初始条件
        init_conditions = self.generate_random_initial(n_samples)
        
        # 为所有样本选择相同的传感器位置
        m = min(32, self.nx)  # 减少传感器数量
        sensor_indices_x = np.linspace(0, self.nx-1, m, dtype=int)
        sensor_indices_y = np.linspace(0, self.ny-1, m, dtype=int)
        
        # 构建数据集
        X_u = []
        X_y = []
        y = []
        
        for i, u0 in enumerate(init_conditions):
            print(f"处理样本 {i+1}/{n_samples}")
            
            # 求解CH方程
            solutions, times = self.solve(u0, n_steps, save_interval)
            
            # 获取传感器值并归一化
            sensors_u0 = u0[np.ix_(sensor_indices_x, sensor_indices_y)].flatten()
            sensors_u0 = np.clip(sensors_u0, -1, 1)  # 限制传感器值范围
            
            # 对每个时间步采样
            for t_idx, t in enumerate(times[1:], 1):
                sol = solutions[t_idx]
                sol = np.clip(sol, -1, 1)  # 限制解的范围
                
                # 随机采样位置
                n_points = 500  # 减少每个时间步的采样点数
                x_indices = np.random.randint(0, self.nx, n_points)
                y_indices = np.random.randint(0, self.ny, n_points)
                
                # 添加到数据集
                X_u.extend([sensors_u0] * n_points)
                X_y.extend([[x_idx*self.dx, y_idx*self.dy, t] for x_idx, y_idx in zip(x_indices, y_indices)])
                y.extend([[sol[x_idx, y_idx]] for x_idx, y_idx in zip(x_indices, y_indices)])
        
        return np.array(X_u), np.array(X_y), np.array(y)
    
    def save_dataset(self, filename, X_u, X_y, y):
        """保存数据集到文件"""
        np.savez_compressed(filename, X_u=X_u, X_y=X_y, y=y)
        print(f"数据集已保存至 {filename}")
    
    def load_dataset(self, filename):
        """从文件加载数据集"""
        data = np.load(filename)
        return data['X_u'], data['X_y'], data['y']

# 测试代码
if __name__ == "__main__":
    # 创建求解器
    solver = CHSolver(nx=64, ny=64, dt=1e-4, epsilon=0.05)
    
    # 创建初始条件
    x = np.linspace(0, solver.Lx, solver.nx)
    y = np.linspace(0, solver.Ly, solver.ny)
    X, Y = np.meshgrid(x, y, indexing='ij')
    
    u0 = 0.5 * np.sin(2*np.pi*X) * np.sin(2*np.pi*Y)
    u0 += np.random.normal(0, 0.1, (solver.nx, solver.ny))
    u0 = np.clip(u0, -1, 1)
    
    # 求解方程
    print("求解CH方程...")
    solutions, times = solver.solve(u0, n_steps=1000, save_interval=50)
    
    # 可视化部分解
    plt.figure(figsize=(15, 10))
    for i, (sol, t) in enumerate(zip(solutions[:9], times[:9])):
        plt.subplot(3, 3, i+1)
        plt.imshow(sol, cmap='coolwarm', vmin=-1, vmax=1)
        plt.colorbar()
        plt.title(f"t = {t:.4f}")
    
    plt.tight_layout()
    plt.savefig('ch_equation_solutions.png')
    plt.close()
    
    # 生成小型数据集进行测试
    print("生成小型数据集...")
    start_time = time.time()
    X_u, X_y, y = solver.generate_dataset(n_samples=2, n_steps=200, save_interval=50)
    print(f"数据集生成耗时: {time.time() - start_time:.2f}秒")
    
    print(f"X_u形状: {X_u.shape}")
    print(f"X_y形状: {X_y.shape}")
    print(f"y形状: {y.shape}")
    
    # 保存数据集
    solver.save_dataset("ch_small_dataset.npz", X_u, X_y, y) 