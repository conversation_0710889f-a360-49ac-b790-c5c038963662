import argparse
import os
import pickle
from pickle import FALSE

import numpy as np
import tensorflow as tf
import random
from sklearn.model_selection import train_test_split
from utils_temp import (
    clear_memory, setup_gpu, setup_mixed_precision, load_temp_data,
    prepare_temp_training_data, visualize_temp_prediction, compare_temp_fields,
    predict_temp_with_original_resolution, TempFieldMonitor, GPUMonitor,
    generate_temp_multi_step_predictions
)

# ========== 设置随机种子确保可重现性 ==========
def set_random_seeds(seed=42):
    """设置所有随机种子确保训练可重现"""
    np.random.seed(seed)
    tf.random.set_seed(seed)
    random.seed(seed)
    os.environ['PYTHONHASHSEED'] = str(seed)

    # 如果使用GPU，启用确定性操作
    if tf.config.list_physical_devices('GPU'):
        try:
            tf.config.experimental.enable_op_determinism()
            print(f"✅ 已设置随机种子: {seed} (包含GPU确定性操作)")
        except Exception as e:
            print(f"⚠️ 已设置随机种子: {seed} (GPU确定性操作设置失败: {str(e)})")
    else:
        print(f"✅ 已设置随机种子: {seed}")

# ========== 温度场模型结构 ==========
def create_temp_model(input_shape, output_dim, use_cosine_decay=False, learning_rate=1e-4):
    with tf.device('/GPU:0'):
        field_input = tf.keras.layers.Input(shape=input_shape)
        # UNet风格结构 + 注意力
        conv1 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(field_input)
        conv1 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv1)
        conv1 = tf.keras.layers.BatchNormalization()(conv1)
        conv1 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(conv1)
        conv1 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv1)
        conv1 = tf.keras.layers.BatchNormalization()(conv1)
        pool1 = tf.keras.layers.MaxPooling2D((2, 2))(conv1)
        conv2 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(pool1)
        conv2 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv2)
        conv2 = tf.keras.layers.BatchNormalization()(conv2)
        conv2 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(conv2)
        conv2 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv2)
        conv2 = tf.keras.layers.BatchNormalization()(conv2)
        pool2 = tf.keras.layers.MaxPooling2D((2, 2))(conv2)
        conv3 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(pool2)
        conv3 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv3)
        conv3 = tf.keras.layers.BatchNormalization()(conv3)
        conv3 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(conv3)
        conv3 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv3)
        conv3 = tf.keras.layers.BatchNormalization()(conv3)
        pool3 = tf.keras.layers.MaxPooling2D((2, 2))(conv3)
        bottleneck = tf.keras.layers.Conv2D(512, (3, 3), activation=None, padding='same')(pool3)
        bottleneck = tf.keras.layers.LeakyReLU(alpha=0.2)(bottleneck)
        bottleneck = tf.keras.layers.BatchNormalization()(bottleneck)
        bottleneck = tf.keras.layers.Conv2D(512, (3, 3), activation=None, padding='same')(bottleneck)
        bottleneck = tf.keras.layers.LeakyReLU(alpha=0.2)(bottleneck)
        bottleneck = tf.keras.layers.BatchNormalization()(bottleneck)
        # 通道注意力
        se = tf.keras.layers.GlobalAveragePooling2D()(bottleneck)
        se = tf.keras.layers.Dense(512 // 16, activation='relu')(se)
        se = tf.keras.layers.Dense(512, activation='sigmoid')(se)
        se = tf.keras.layers.Reshape((1, 1, 512))(se)
        bottleneck = tf.keras.layers.Multiply()([bottleneck, se])
        # 空间注意力
        spatial = tf.keras.layers.Conv2D(1, (7, 7), padding='same', activation='sigmoid')(bottleneck)
        bottleneck = tf.keras.layers.Multiply()([bottleneck, spatial])
        up3 = tf.keras.layers.Conv2DTranspose(256, (2, 2), strides=(2, 2), padding='same')(bottleneck)
        concat3 = tf.keras.layers.Concatenate()([up3, conv3])
        conv6 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(concat3)
        conv6 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv6)
        conv6 = tf.keras.layers.BatchNormalization()(conv6)
        conv6 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(conv6)
        conv6 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv6)
        conv6 = tf.keras.layers.BatchNormalization()(conv6)
        up2 = tf.keras.layers.Conv2DTranspose(128, (2, 2), strides=(2, 2), padding='same')(conv6)
        concat2 = tf.keras.layers.Concatenate()([up2, conv2])
        conv7 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(concat2)
        conv7 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv7)
        conv7 = tf.keras.layers.BatchNormalization()(conv7)
        conv7 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(conv7)
        conv7 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv7)
        conv7 = tf.keras.layers.BatchNormalization()(conv7)
        up1 = tf.keras.layers.Conv2DTranspose(64, (2, 2), strides=(2, 2), padding='same')(conv7)
        concat1 = tf.keras.layers.Concatenate()([up1, conv1])
        conv8 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(concat1)
        conv8 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv8)
        conv8 = tf.keras.layers.BatchNormalization()(conv8)
        conv8 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(conv8)
        conv8 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv8)
        conv8 = tf.keras.layers.BatchNormalization()(conv8)
        gap = tf.keras.layers.GlobalAveragePooling2D()(conv8)
        gap_bottleneck = tf.keras.layers.GlobalAveragePooling2D()(bottleneck)
        gap_conv6 = tf.keras.layers.GlobalAveragePooling2D()(conv6)
        gap_conv7 = tf.keras.layers.GlobalAveragePooling2D()(conv7)
        multi_scale = tf.keras.layers.Concatenate()([gap, gap_bottleneck, gap_conv6, gap_conv7])
        x = tf.keras.layers.Dense(1024, activation=None)(multi_scale)
        x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Dropout(0.5)(x)
        res = tf.keras.layers.Dense(1024)(multi_scale)
        x = tf.keras.layers.add([x, res])
        x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
        x = tf.keras.layers.Dense(512, activation=None)(x)
        x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Dropout(0.4)(x)
        res2 = tf.keras.layers.Dense(512)(x)
        x = tf.keras.layers.Dense(512, activation=None)(x)
        x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.add([x, res2])
        branch_output = tf.keras.layers.Dense(output_dim)(x)
        model = tf.keras.Model(inputs=field_input, outputs=branch_output)
    if use_cosine_decay:
        lr_schedule = tf.keras.optimizers.schedules.CosineDecay(
            initial_learning_rate=learning_rate,
            decay_steps=2000,
            alpha=learning_rate * 0.01  # 最小学习率为初始学习率的1%
        )
        optimizer = tf.keras.optimizers.Adam(learning_rate=lr_schedule, clipnorm=0.5)
    else:
        optimizer = tf.keras.optimizers.Adam(learning_rate=learning_rate, clipnorm=0.5)
    if tf.config.list_physical_devices('GPU'):
        optimizer = tf.keras.mixed_precision.LossScaleOptimizer(optimizer)
    def combined_loss(y_true, y_pred):
        y_true = tf.cast(y_true, tf.float32)
        y_pred = tf.cast(y_pred, tf.float32)
        mse = tf.keras.losses.MeanSquaredError()(y_true, y_pred)
        mae = tf.keras.losses.MeanAbsoluteError()(y_true, y_pred)
        huber = tf.keras.losses.Huber(delta=1.0)(y_true, y_pred)
        return 0.6 * mse + 0.2 * mae + 0.2 * huber
    model.compile(optimizer=optimizer, loss=combined_loss, metrics=['mae', 'mse'])
    return model

# ========== 参数解析 ==========
def parse_args():
    parser = argparse.ArgumentParser(description='温度场训练参数')
    parser.add_argument('--temp_data_dir', type=str, default='E:/Freezedata/TemperatureField_Data/', help='温度场数据目录')
    parser.add_argument('--model_dir', type=str, default='./models', help='模型保存目录')
    parser.add_argument('--results_dir', type=str, default='./models/results', help='结果保存目录')
    parser.add_argument('--epochs', type=int, default=150, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=2, help='批次大小')
    parser.add_argument('--stride', type=int, default=5, help='数据加载步长')
    parser.add_argument('--max_samples', type=int, default=5000, help='最大加载样本数')
    parser.add_argument('--start_step', type=int, default=0, help='起始步数')
    parser.add_argument('--end_step', type=int, default=3050, help='结束步数')
    parser.add_argument('--downsample', type=int, default=4, help='空间降采样因子')
    parser.add_argument('--use_data_augmentation', action='store_true', default=True, help='是否使用数据增强')
    parser.add_argument('--use_cosine_decay', action='store_true', default=False, help='是否使用余弦退火学习率')
    parser.add_argument('--load_model', action='store_true', help='是否加载已有模型')
    parser.add_argument('--save_model', action='store_true', default=True, help='是否保存模型')
    parser.add_argument('--generate_multi_step', action='store_true', default=True, help='是否生成多时间步预测图')
    parser.add_argument('--multi_step_start', type=int, default=500, help='多步预测起始步')
    parser.add_argument('--multi_step_end', type=int, default=3000, help='多步预测结束步')
    parser.add_argument('--multi_step_interval', type=int, default=500, help='多步预测间隔')

    # 新增监控和控制参数
    parser.add_argument('--enable_gpu_monitor', action='store_true', default=True, help='是否启用GPU监控')
    parser.add_argument('--enable_temp_monitor', action='store_true', default=True, help='是否启用温度场训练监控')
    parser.add_argument('--use_early_stopping', action='store_true', default=True, help='是否使用早停机制')
    parser.add_argument('--patience', type=int, default=200, help='早停耐心值')
    parser.add_argument('--min_delta', type=float, default=1e-4, help='早停最小改善值')
    parser.add_argument('--enable_detailed_memory', action='store_true', default=False, help='是否启用详细内存监控')
    parser.add_argument('--memory_check_interval', type=int, default=10, help='内存检查间隔(批次)')
    parser.add_argument('--enable_lr_scheduler', action='store_true', default=False, help='是否启用学习率调度器')
    parser.add_argument('--lr_reduce_factor', type=float, default=0.5, help='学习率衰减因子')
    parser.add_argument('--lr_reduce_patience', type=int, default=10, help='学习率衰减耐心值')
    parser.add_argument('--learning_rate', type=float, default=1e-4, help='初始学习率')
    parser.add_argument('--smooth_mode', action='store_true', default=True, help='启用平滑模式（降低噪声）')
    parser.add_argument('--random_seed', type=int, default=42, help='随机种子，确保训练可重现')
    return parser.parse_args()



# ========== 主流程 ==========
def main():
    args = parse_args()

    # 打印训练配置信息
    print("\n🎯 温度场训练配置")
    print("=" * 50)
    print(f"   学习率: {args.learning_rate}")
    print(f"   训练轮数: {args.epochs}")
    print(f"   早停启用: {args.use_early_stopping}")
    if args.use_early_stopping:
        print(f"   早停耐心: {args.patience}")
        print(f"   最小改善值: {args.min_delta}")
    print(f"   平滑模式: {args.smooth_mode}")
    print(f"   随机种子: {args.random_seed}")
    print(f"   多步预测: {args.generate_multi_step}")
    if args.generate_multi_step:
        print(f"   预测范围: {args.multi_step_start} - {args.multi_step_end} (间隔: {args.multi_step_interval})")
    print("=" * 50)

    # ✅ 设置随机种子确保可重现性
    set_random_seeds(args.random_seed)

    os.makedirs(args.model_dir, exist_ok=True)
    os.makedirs(args.results_dir, exist_ok=True)
    # 配置GPU
    gpu_available = setup_gpu()
    if gpu_available:
        setup_mixed_precision()
    # 加载数据
    print('加载温度场数据...')
    temp_data = load_temp_data(
        args.temp_data_dir,
        start_step=args.start_step, end_step=args.end_step,
        stride=args.stride, max_samples=args.max_samples
    )
    clear_memory()

    # 详细内存监控
    if args.enable_detailed_memory:
        import psutil
        memory_before = psutil.virtual_memory()
        print(f"数据加载后内存使用: {memory_before.percent}% ({memory_before.used/1024/1024/1024:.2f}GB)")

    # 训练数据准备
    print('准备训练数据...')
    X_temp, _, y_temp, temp_norm_params = prepare_temp_training_data(
        temp_data, downsample_factor=args.downsample,
        use_data_augmentation=args.use_data_augmentation
    )
    del temp_data
    clear_memory()

    # 分割训练集和验证集
    print('分割训练集和验证集...')
    X_train, X_val, y_train, y_val = train_test_split(
        X_temp, y_temp, test_size=0.2, random_state=42, shuffle=True
    )
    print(f'训练集大小: {X_train.shape[0]}, 验证集大小: {X_val.shape[0]}')

    # 详细内存监控
    if args.enable_detailed_memory:
        memory_after = psutil.virtual_memory()
        print(f"训练数据准备后内存使用: {memory_after.percent}% ({memory_after.used/1024/1024/1024:.2f}GB)")
        print(f"训练数据占用内存: {(X_temp.nbytes + y_temp.nbytes)/1024/1024/1024:.2f}GB")

    # 🔧 修复归一化参数问题：只在首次训练时保存归一化参数
    temp_norm_path = os.path.join(args.model_dir, 'temp_norm_params.npy')
    if temp_norm_params and not os.path.exists(temp_norm_path):
        np.save(temp_norm_path, temp_norm_params, allow_pickle=True)
        print(f'🆕 温度场归一化参数已保存到: {temp_norm_path}')
    elif os.path.exists(temp_norm_path):
        # 如果已有归一化参数文件，使用已有的参数而不是新计算的参数
        existing_params = np.load(temp_norm_path, allow_pickle=True).item()
        print(f'🔄 使用已有归一化参数确保一致性: 方法={existing_params.get("method", "unknown")}')
        if 'mean' in existing_params and 'std' in existing_params and temp_norm_params:
            print(f'  已有参数 - 均值={existing_params["mean"]:.6f}, 标准差={existing_params["std"]:.6f}')
            print(f'  新计算参数 - 均值={temp_norm_params["mean"]:.6f}, 标准差={temp_norm_params["std"]:.6f}')
        temp_norm_params = existing_params  # 使用已有参数
    else:
        np.save(temp_norm_path, temp_norm_params, allow_pickle=True)
        print(f'温度场归一化参数已保存到: {temp_norm_path}')
    # 构建模型
    temp_model_path = os.path.join(args.model_dir, 'temp_model.keras')
    if args.load_model and os.path.exists(temp_model_path):
        print(f'加载已有模型: {temp_model_path}')
        model = tf.keras.models.load_model(temp_model_path)
        history = None
    else:
        print('开始训练温度场模型...')

        model = create_temp_model(X_temp[0].shape, y_temp[0].shape[0],
                                 use_cosine_decay=args.use_cosine_decay,
                                 learning_rate=args.learning_rate)
        # 设置回调
        callbacks = []

        # 早停机制 - 修复：监控验证损失而不是训练损失
        if args.use_early_stopping:
            early_stopping = tf.keras.callbacks.EarlyStopping(
                monitor='val_loss',  # ✅ 修复：监控验证损失
                patience=args.patience,
                min_delta=args.min_delta,
                verbose=1,
                restore_best_weights=True
            )
            callbacks.append(early_stopping)
            print(f"✅ 已启用早停机制: 监控val_loss, 耐心值={args.patience}, 最小改善值={args.min_delta}")

        # GPU监控
        if args.enable_gpu_monitor:
            gpu_monitor = GPUMonitor(enabled=True)
            callbacks.append(gpu_monitor)
            print("已启用GPU监控")

        # 温度场训练监控
        if args.enable_temp_monitor:
            temp_monitor = TempFieldMonitor(model_type='temp', enabled=True)
            callbacks.append(temp_monitor)
            print("已启用温度场训练监控")

        # 学习率调度器 - 修复：监控验证损失
        if args.enable_lr_scheduler:
            lr_scheduler = tf.keras.callbacks.ReduceLROnPlateau(
                monitor='val_loss',  # ✅ 修复：监控验证损失
                factor=args.lr_reduce_factor,
                patience=args.lr_reduce_patience,
                verbose=1,
                min_delta=args.min_delta
            )
            callbacks.append(lr_scheduler)
            print(f"✅ 已启用学习率调度器: 监控val_loss, 衰减因子={args.lr_reduce_factor}, 耐心值={args.lr_reduce_patience}")

        # 模型检查点 - 修复：监控验证损失
        if args.save_model:
            checkpoint_path = os.path.join(args.model_dir, 'temp_model_checkpoint.keras')
            checkpoint = tf.keras.callbacks.ModelCheckpoint(
                checkpoint_path,
                monitor='val_loss',  # ✅ 修复：监控验证损失
                save_best_only=True,
                verbose=1
            )
            callbacks.append(checkpoint)
            print(f"✅ 已启用模型检查点: 监控val_loss, 保存到 {checkpoint_path}")

        # 训练模型 - 修复：使用验证集
        print("\n开始训练温度场模型...")
        print(f"训练集: {X_train.shape}, 验证集: {X_val.shape}")
        history = model.fit(
            X_train, y_train,  # ✅ 修复：使用分割后的训练集
            validation_data=(X_val, y_val),  # ✅ 修复：添加验证集
            epochs=args.epochs,
            batch_size=args.batch_size,
            callbacks=callbacks,
            verbose=1
        )
        # 总是保存模型（训练完成后）
        print(f'保存模型到: {temp_model_path}')
        model.save(temp_model_path)
        print(f'✅ 模型已保存到: {temp_model_path}')

        # 保存训练历史
        if history is not None:
            with open(os.path.join(args.results_dir, 'temp_history.pkl'), 'wb') as f:
                pickle.dump(history.history, f)
            print(f'✅ 训练历史已保存到: {os.path.join(args.results_dir, "temp_history.pkl")}')
    # 预测与可视化
    print('加载预测数据...')
    # 加载连续的两个时间步：输入步和目标步（用于预测）
    input_step = args.end_step - 1  # 输入：倒数第二步
    target_step = args.end_step     # 目标：最后一步

    print(f'加载温度场输入数据 (第{input_step}步) 和目标数据 (第{target_step}步)')

    pred_temp_data = load_temp_data(
        args.temp_data_dir,
        start_step=input_step, end_step=target_step,
        stride=1, max_samples=2
    )

    if len(pred_temp_data) < 2:
        print(f"警告: 需要2个时间步的数据，但只加载到{len(pred_temp_data)}个")
        pred_temp_data = load_temp_data(
            args.temp_data_dir,
            start_step=max(0, args.end_step-5), end_step=args.end_step,
            stride=1, max_samples=10
        )
        if len(pred_temp_data) < 2:
            print("错误: 无法加载足够的温度场数据进行预测")
            return

    # 分离输入数据（用于预测）
    input_data = pred_temp_data[-2]   # 倒数第二个作为输入

    # 🔧 修复Ground Truth显示问题：Ground Truth单独从文件加载，与多时间步预测保持一致
    print("🔄 为确保Ground Truth显示一致性，单独从文件加载目标数据...")
    target_temp_data = load_temp_data(
        args.temp_data_dir,
        start_step=target_step, end_step=target_step,
        stride=1, max_samples=1
    )

    if len(target_temp_data) == 0:
        print("警告: 无法单独加载目标数据，使用预处理数据")
        target_data = pred_temp_data[-1]  # 备选方案
    else:
        target_data = target_temp_data[0]  # 直接从文件加载的Ground Truth

    print(f'温度场输入数据形状: {input_data.shape}')
    print(f'温度场目标数据形状: {target_data.shape}')
    print(f'目标数据原始范围: [{target_data.min():.2f}, {target_data.max():.2f}], 均值: {target_data.mean():.2f}')
    print(f'✅ Ground Truth现在使用与多时间步预测相同的加载方式')

    # 加载归一化参数
    temp_norm_path = os.path.join(args.model_dir, 'temp_norm_params.npy')
    temp_norm_params = None
    if os.path.exists(temp_norm_path):
        temp_norm_params = np.load(temp_norm_path, allow_pickle=True).item()
        print(f'加载温度场归一化参数: 方法={temp_norm_params.get("method", "unknown")}')
        if 'mean' in temp_norm_params and 'std' in temp_norm_params:
            print(f'  均值={temp_norm_params["mean"]:.6f}, 标准差={temp_norm_params["std"]:.6f}')

    # 使用输入数据进行预测
    temp_pred = predict_temp_with_original_resolution(
        model, input_data, downsample_factor=args.downsample, norm_params=temp_norm_params
    )

    # 检查预测结果
    if temp_pred is not None:
        print(f'温度场预测结果形状: {temp_pred.shape}')

        # 🔧 修复Ground Truth显示问题：对Ground Truth应用相同的归一化→反归一化处理
        # 确保Ground Truth和预测结果在同一数值空间
        processed_target_data = target_data.copy()
        if temp_norm_params is not None:
            print("对Ground Truth应用归一化→反归一化处理，确保与预测结果数值空间一致")
            if temp_norm_params.get('method') == 'zscore' and 'mean' in temp_norm_params and 'std' in temp_norm_params:
                # 归一化然后立即反归一化，确保数值范围一致
                normalized_gt = (processed_target_data - temp_norm_params['mean']) / (temp_norm_params['std'] + 1e-10)
                processed_target_data = normalized_gt * temp_norm_params['std'] + temp_norm_params['mean']
                print(f"Ground Truth经过归一化处理后范围: [{processed_target_data.min():.2f}, {processed_target_data.max():.2f}]")
            elif temp_norm_params.get('method') == 'minmax' and 'min' in temp_norm_params and 'max' in temp_norm_params:
                # MinMax归一化然后立即反归一化
                normalized_gt = (processed_target_data - temp_norm_params['min']) / (temp_norm_params['max'] - temp_norm_params['min'] + 1e-10)
                processed_target_data = normalized_gt * (temp_norm_params['max'] - temp_norm_params['min']) + temp_norm_params['min']
                print(f"Ground Truth经过归一化处理后范围: [{processed_target_data.min():.2f}, {processed_target_data.max():.2f}]")

        # 使用处理后的Ground Truth进行可视化
        visualize_temp_prediction(processed_target_data, temp_pred, args.temp_data_dir, save_dir=args.results_dir)
        np.save(os.path.join(args.results_dir, 'temp_prediction.npy'), temp_pred)
        compare_temp_fields(processed_target_data, temp_pred, '温度场误差分析', args.results_dir)
        print(f"温度场预测完成: 输入第{input_step}步 -> 预测第{target_step}步")
    else:
        print("警告: 温度场预测失败，跳过可视化步骤")
    # 训练历史可视化
    if history is not None:
        try:
            import matplotlib.pyplot as plt

            # 创建单个图，包含两条曲线
            _, ax1 = plt.subplots(1, 1, figsize=(12, 8))

            # 损失曲线 (左y轴)
            color1 = 'tab:blue'
            ax1.set_xlabel('Epoch', fontsize=12)
            ax1.set_ylabel('Loss', color=color1, fontsize=12)
            line1 = ax1.plot(history.history['loss'], label='Training Loss', linewidth=2, color=color1)
            ax1.tick_params(axis='y', labelcolor=color1)
            ax1.grid(True, alpha=0.3)

            # MAE曲线 (右y轴)
            if 'mae' in history.history:
                ax2 = ax1.twinx()  # 创建共享x轴的第二个y轴
                color2 = 'tab:red'
                ax2.set_ylabel('MAE', color=color2, fontsize=12)
                line2 = ax2.plot(history.history['mae'], label='Training MAE', linewidth=2, color=color2)
                ax2.tick_params(axis='y', labelcolor=color2)

                # 合并图例
                lines = line1 + line2
                labels = [str(l.get_label()) for l in lines]
                ax1.legend(lines, labels, loc='upper right', fontsize=10)
            else:
                ax1.legend(fontsize=10)

            plt.title('Temperature Field Training Progress', fontsize=14, pad=20)
            plt.tight_layout()

            history_path = os.path.join(args.results_dir, 'temp_training_history.png')
            plt.savefig(history_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"✅ 温度场训练历史图已保存到: {history_path}")
        except Exception as e:
            print(f"❌ 保存训练历史图失败: {str(e)}")
    else:
        print("⚠️ 没有训练历史数据（可能是加载了已有模型）")
    # 多步预测
    if args.generate_multi_step:
        print("🎬 开始生成多时间步预测...")
        generate_temp_multi_step_predictions(
            model, args.temp_data_dir,
            args.multi_step_start, args.multi_step_end, args.multi_step_interval,
            downsample_factor=args.downsample, norm_params=temp_norm_params, save_dir=args.results_dir
        )
    print('温度场训练与预测完成！')

if __name__ == '__main__':
    main() 