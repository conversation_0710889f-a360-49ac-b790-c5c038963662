#!/usr/bin/env python3
"""
时间步预测对比工具
支持显示同一行/列在不同时间步的Ground Truth和Prediction对比
"""

import numpy as np
import matplotlib.pyplot as plt
import os
import argparse
from datetime import datetime
import pandas as pd
import tensorflow as tf
from tensorflow.keras.losses import MeanSquaredError, MeanAbsoluteError, Huber

# 设置matplotlib中文字体和美观样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial']
plt.rcParams['axes.unicode_minus'] = False
plt.style.use('seaborn-v0_8-whitegrid')

def load_temp_data_single_step(temp_data_dir, step):
    """加载单个时间步的温度场数据"""
    temp_file = os.path.join(temp_data_dir, f"温度场第{step}步.csv")
    if not os.path.exists(temp_file):
        raise FileNotFoundError(f"文件不存在: {temp_file}")
    
    temp_data = pd.read_csv(temp_file, header=None, encoding='utf-8').values.astype(np.float32)
    return temp_data

def extract_line_data(data_array, line_type='row', line_index=400):
    """从2D数组中提取特定行或列的数据"""
    if line_type == 'row':
        if line_index >= data_array.shape[0]:
            raise ValueError(f"行索引 {line_index} 超出数组范围 {data_array.shape[0]}")
        return data_array[line_index, :]
    elif line_type == 'column':
        if line_index >= data_array.shape[1]:
            raise ValueError(f"列索引 {line_index} 超出数组范围 {data_array.shape[1]}")
        return data_array[:, line_index]
    else:
        raise ValueError("line_type 必须是 'row' 或 'column'")

def predict_temp_with_model(model, input_data, norm_params=None, downsample_factor=4):
    """使用模型预测温度场 - 支持降采样"""
    try:
        print(f"开始预测，输入数据形状: {input_data.shape}")

        # 归一化输入
        if norm_params is not None:
            if norm_params['method'] == 'zscore':
                normalized_input = (input_data - norm_params['mean']) / (norm_params['std'] + 1e-10)
            elif norm_params['method'] == 'minmax':
                normalized_input = (input_data - norm_params['min']) / (norm_params['max'] - norm_params['min'] + 1e-10)
            else:
                normalized_input = input_data
        else:
            normalized_input = input_data

        # 降采样到模型期望的尺寸
        if downsample_factor > 1:
            try:
                from scipy.ndimage import zoom
                zoom_factor = 1.0 / downsample_factor
                downsampled_input = zoom(normalized_input, zoom_factor, order=3)
                print(f"降采样后形状: {downsampled_input.shape}")
            except Exception as e:
                print(f"降采样失败: {str(e)}，使用简单步长降采样")
                downsampled_input = normalized_input[::downsample_factor, ::downsample_factor]
                print(f"简单降采样后形状: {downsampled_input.shape}")
        else:
            downsampled_input = normalized_input

        # 重塑为模型输入格式
        model_input = downsampled_input.reshape(1, downsampled_input.shape[0], downsampled_input.shape[1], 1)
        print(f"模型输入形状: {model_input.shape}")

        # 预测
        prediction = model.predict(model_input, verbose=0)
        print(f"模型输出形状: {prediction.shape}")

        # 重塑预测结果
        pred_shape = downsampled_input.shape
        prediction_reshaped = prediction.reshape(pred_shape)

        # 上采样回原始分辨率
        if downsample_factor > 1:
            try:
                from scipy.ndimage import zoom
                zoom_factor = downsample_factor
                prediction_upsampled = zoom(prediction_reshaped.astype(np.float64), zoom_factor, order=1)

                # 确保输出尺寸与输入一致
                if prediction_upsampled.shape != input_data.shape:
                    zoom_factor_x = input_data.shape[0] / prediction_upsampled.shape[0]
                    zoom_factor_y = input_data.shape[1] / prediction_upsampled.shape[1]
                    prediction_upsampled = zoom(prediction_upsampled, (zoom_factor_x, zoom_factor_y), order=1)

                prediction_upsampled = prediction_upsampled.astype(np.float32)
                print(f"上采样后形状: {prediction_upsampled.shape}")
            except Exception as e:
                print(f"上采样失败: {str(e)}")
                prediction_upsampled = prediction_reshaped
        else:
            prediction_upsampled = prediction_reshaped

        # 反归一化
        if norm_params is not None:
            if norm_params['method'] == 'zscore':
                final_prediction = prediction_upsampled * norm_params['std'] + norm_params['mean']
            elif norm_params['method'] == 'minmax':
                final_prediction = prediction_upsampled * (norm_params['max'] - norm_params['min']) + norm_params['min']
            else:
                final_prediction = prediction_upsampled
        else:
            final_prediction = prediction_upsampled

        print(f"最终预测结果形状: {final_prediction.shape}")
        return final_prediction

    except Exception as e:
        print(f"预测失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def plot_temporal_gt_pred_comparison(temp_data_dir, model, norm_params, time_steps,
                                   line_type='row', line_index=400, save_path=None, downsample_factor=4):
    """
    绘制不同时间步同一行/列的Ground Truth和Prediction对比
    """
    # 创建美观的图形 - 单个图表显示GT和Prediction对比
    fig, ax = plt.subplots(figsize=(16, 10), facecolor='white')
    ax.set_facecolor('white')
    
    # 定义美观的颜色序列
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
              '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
    
    # 存储数据
    gt_lines_data = []
    pred_lines_data = []
    valid_steps = []
    
    # 处理每个时间步
    for i, step in enumerate(time_steps):
        try:
            # 加载Ground Truth数据
            gt_data = load_temp_data_single_step(temp_data_dir, step)
            
            # 使用前一步作为输入进行预测
            if step > 0:
                input_data = load_temp_data_single_step(temp_data_dir, step - 1)
                # 添加降采样因子参数 - 与训练时保持一致
                pred_data = predict_temp_with_model(model, input_data, norm_params, downsample_factor)

                if pred_data is None:
                    print(f"⚠️  第{step}步预测失败，跳过")
                    continue
            else:
                print(f"⚠️  第{step}步是起始步，无法预测，跳过")
                continue
            
            # 提取线数据
            gt_line = extract_line_data(gt_data, line_type, line_index)
            pred_line = extract_line_data(pred_data, line_type, line_index)
            x_coords = np.arange(len(gt_line))
            
            color = colors[i % len(colors)]
            line_label = f'Step {step}'
            
            # 在同一图表中绘制Ground Truth和Prediction
            # Ground Truth (实线)
            ax.plot(x_coords, gt_line, color=color, linewidth=2.5,
                   label=f'GT {line_label}', alpha=0.9, linestyle='-')

            # Prediction (虚线)
            ax.plot(x_coords, pred_line, color=color, linewidth=2.5,
                   label=f'Pred {line_label}', alpha=0.9, linestyle='--')
            
            gt_lines_data.append(gt_line)
            pred_lines_data.append(pred_line)
            valid_steps.append(step)
            
            print(f"✅ 第{step}步: GT范围[{gt_line.min():.2f}, {gt_line.max():.2f}], "
                  f"Pred范围[{pred_line.min():.2f}, {pred_line.max():.2f}]")
            
        except Exception as e:
            print(f"⚠️  跳过第{step}步: {str(e)}")
            continue
    
    if not gt_lines_data:
        print("❌ 没有有效的数据可以绘制")
        return None
    
    # 设置图形属性
    axis_label = 'X' if line_type == 'row' else 'Y'
    line_label = f'Row {line_index}' if line_type == 'row' else f'Col {line_index}'

    # 设置单个图表样式
    ax.set_xlabel(f'{axis_label} Coordinate', fontsize=12, fontweight='bold')
    ax.set_ylabel('Temperature (K)', fontsize=12, fontweight='bold')
    ax.set_title(f'Ground Truth vs Prediction - {line_label} Temporal Evolution',
                fontsize=14, fontweight='bold', pad=15)

    # 优化图例显示 - 将GT和Pred成对显示
    handles, labels = ax.get_legend_handles_labels()
    # 重新排序图例，让同一时间步的GT和Pred相邻显示
    reordered_handles = []
    reordered_labels = []
    n_steps = len(valid_steps)
    for i in range(n_steps):
        # GT在前，Pred在后
        gt_idx = i * 2
        pred_idx = i * 2 + 1
        if gt_idx < len(handles):
            reordered_handles.append(handles[gt_idx])
            reordered_labels.append(labels[gt_idx])
        if pred_idx < len(handles):
            reordered_handles.append(handles[pred_idx])
            reordered_labels.append(labels[pred_idx])

    ax.legend(reordered_handles, reordered_labels, fontsize=10, frameon=True,
             fancybox=True, shadow=True, loc='upper right',
             bbox_to_anchor=(0.98, 0.98), ncol=2)
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)

    # 设置坐标轴样式
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(0.8)
    ax.spines['bottom'].set_linewidth(0.8)
    
    # 计算统计信息
    all_gt_data = np.concatenate(gt_lines_data)
    all_pred_data = np.concatenate(pred_lines_data)
    
    # 计算误差统计
    errors = []
    for gt_line, pred_line in zip(gt_lines_data, pred_lines_data):
        error = np.mean(np.abs(gt_line - pred_line))
        errors.append(error)
    
    mean_error = np.mean(errors)
    max_error = np.max(errors)
    
    # 添加统计信息框到第二个子图
    stats_text = f'Time Steps: {len(valid_steps)}\n' \
                f'Step Range: {valid_steps[0]} → {valid_steps[-1]}\n' \
                f'Mean MAE: {mean_error:.3f}\n' \
                f'Max MAE: {max_error:.3f}\n' \
                f'GT Range: [{all_gt_data.min():.1f}, {all_gt_data.max():.1f}]\n' \
                f'Pred Range: [{all_pred_data.min():.1f}, {all_pred_data.max():.1f}]'
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
           verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5',
           facecolor='lightcyan', alpha=0.8, edgecolor='teal', linewidth=1))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"✅ 时间步预测对比图已保存到: {save_path}")
    
    plt.show()
    return gt_lines_data, pred_lines_data, valid_steps

def interactive_temporal_pred_console():
    """交互式时间步预测对比控制台"""
    print("🎯 " + "="*60)
    print("    时间步预测对比可视化工具")
    print("="*64)

    # 获取降采样因子
    default_downsample = 4
    while True:
        downsample_input = input(f"🔍 请输入降采样因子 (默认: {default_downsample}): ").strip()
        if not downsample_input:
            downsample_factor = default_downsample
            break
        try:
            downsample_factor = int(downsample_input)
            if downsample_factor < 1:
                print("❌ 降采样因子必须大于等于1")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的整数")
    
    # 获取数据目录
    default_data_dir = 'E:/Freezedata/TemperatureField_Data/'
    data_dir = input(f"📂 请输入温度场数据目录路径 (默认: {default_data_dir}): ").strip()
    if not data_dir:
        data_dir = default_data_dir
    
    if not os.path.exists(data_dir):
        print(f"❌ 目录不存在: {data_dir}")
        return None
    
    # 获取模型路径 - 自动检测可用模型
    available_model = find_available_model()
    if available_model:
        print(f"🔍 检测到可用模型: {available_model}")
        use_detected = input("是否使用检测到的模型? (Y/n): ").strip().lower()
        if use_detected != 'n':
            model_path = available_model
        else:
            model_path = input("🤖 请输入模型路径: ").strip()
            if not os.path.exists(model_path):
                print(f"❌ 模型文件不存在: {model_path}")
                return None
    else:
        model_path = input("🤖 请输入模型路径: ").strip()
        if not os.path.exists(model_path):
            print(f"❌ 模型文件不存在: {model_path}")
            return None
    
    # 获取线类型
    print("\n📊 选择对比类型:")
    print("  1. Row (行) - 水平截面的时间演化")
    print("  2. Column (列) - 垂直截面的时间演化")
    
    while True:
        choice = input("请选择 (1/2, 默认1): ").strip()
        if choice == '2':
            line_type = 'column'
            break
        elif choice == '1' or choice == '':
            line_type = 'row'
            break
        else:
            print("❌ 请输入 1 或 2")
    
    # 获取行/列索引
    default_index = 400
    while True:
        index_input = input(f"📍 请输入{'行' if line_type == 'row' else '列'}索引 (默认: {default_index}): ").strip()
        if not index_input:
            line_index = default_index
            break
        try:
            line_index = int(index_input)
            if line_index < 0:
                print("❌ 索引必须为非负整数")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的整数")
    
    # 获取时间步
    print(f"\n⏰ 请输入要对比的时间步:")
    print("   支持格式:")
    print("   - 多个值: 500,1000,1500,2000")
    print("   - 范围: 500-2000-500 (从500到2000，步长500)")
    
    while True:
        steps_input = input("请输入时间步: ").strip()
        if not steps_input:
            print("❌ 请输入至少一个时间步")
            continue
        
        try:
            from temporal_line_comparison import parse_time_steps
            time_steps = parse_time_steps(steps_input)
            if not time_steps:
                print("❌ 没有解析到有效的时间步")
                continue
            print(f"✅ 解析到时间步: {time_steps}")
            break
        except Exception as e:
            print(f"❌ 解析时间步失败: {str(e)}")
    
    # 获取保存路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    default_save_name = f"temporal_pred_{line_type}_{line_index}_{timestamp}.png"
    default_save_path = os.path.join(os.path.dirname(data_dir), 'results', default_save_name)
    
    print(f"\n💾 图片保存设置:")
    save_path = input(f"请输入保存路径 (默认: {default_save_path}): ").strip()
    if not save_path:
        save_path = default_save_path
    
    # 确保保存目录存在
    save_dir = os.path.dirname(save_path)
    if save_dir and not os.path.exists(save_dir):
        os.makedirs(save_dir, exist_ok=True)
        print(f"📁 已创建目录: {save_dir}")
    
    print("\n" + "="*64)
    print("📋 配置摘要:")
    print(f"   数据目录: {data_dir}")
    print(f"   模型路径: {model_path}")
    print(f"   对比类型: {line_type} (索引: {line_index})")
    print(f"   时间步: {time_steps}")
    print(f"   保存路径: {save_path}")
    print("="*64)
    
    return {
        'data_dir': data_dir,
        'model_path': model_path,
        'line_type': line_type,
        'line_index': line_index,
        'time_steps': time_steps,
        'save_path': save_path,
        'downsample_factor': downsample_factor
    }

# 定义自定义损失函数 - 与train_temp.py中相同
def combined_loss(y_true, y_pred):
    """
    组合损失函数: 0.6*MSE + 0.2*MAE + 0.2*Huber
    """
    y_true = tf.cast(y_true, tf.float32)
    y_pred = tf.cast(y_pred, tf.float32)
    mse = MeanSquaredError()(y_true, y_pred)
    mae = MeanAbsoluteError()(y_true, y_pred)
    huber = Huber(delta=1.0)(y_true, y_pred)
    return 0.6 * mse + 0.2 * mae + 0.2 * huber

def find_available_model():
    """自动查找可用的模型文件"""
    model_candidates = [
        './models/temp_model.keras',
        './models/smooth_temp_final.keras',
        './models/smooth_temp_model.keras'
    ]

    for model_path in model_candidates:
        if os.path.exists(model_path):
            return model_path

    return None

def main():
    parser = argparse.ArgumentParser(description='时间步预测对比可视化')
    parser.add_argument('--interactive', '-i', action='store_true',
                       help='启用交互式控制台')
    parser.add_argument('--data_dir', type=str, default='E:/Freezedata/TemperatureField_Data/',
                       help='温度场数据目录路径')
    parser.add_argument('--model_path', type=str, default=None,
                       help='模型文件路径')
    parser.add_argument('--line_type', type=str, choices=['row', 'column'], default='row',
                       help='对比类型：row(行) 或 column(列)')
    parser.add_argument('--line_index', type=int, default=400,
                       help='行或列索引')
    parser.add_argument('--time_steps', type=str, default='500,1000,2000,3000',
                       help='时间步，支持逗号分隔或范围格式')
    parser.add_argument('--downsample_factor', type=int, default=4,
                       help='降采样因子，应与训练时保持一致')
    parser.add_argument('--save_path', type=str, default='./models/results/1-temp/1-temp_profile.png',
                       help='图片保存路径')
    
    args = parser.parse_args()
    
    # 如果启用交互模式
    if args.interactive:
        config = interactive_temporal_pred_console()
        if config is None:
            return
        
        args.data_dir = config['data_dir']
        args.model_path = config['model_path']
        args.line_type = config['line_type']
        args.line_index = config['line_index']
        args.time_steps = config['time_steps']
        args.save_path = config['save_path']
        args.downsample_factor = config['downsample_factor']
    else:
        # 解析命令行参数中的时间步
        from temporal_line_comparison import parse_time_steps
        args.time_steps = parse_time_steps(args.time_steps)
    
    # 自动查找或使用指定的模型
    if args.model_path is None:
        args.model_path = find_available_model()
        if args.model_path is None:
            print("❌ 未找到可用的模型文件")
            print("请检查以下路径是否存在模型文件:")
            print("  - ./models/temp_model.keras")
            print("  - ./models/smooth_temp_final.keras")
            print("  - ./models/smooth_temp_model.keras")
            return
        else:
            print(f"🔍 自动找到模型文件: {args.model_path}")

    # 加载模型 - 包含自定义损失函数
    try:
        print(f"📂 加载模型: {args.model_path}")

        # 定义自定义对象字典
        custom_objects = {
            'combined_loss': combined_loss
        }

        model = tf.keras.models.load_model(args.model_path, custom_objects=custom_objects)
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        print("💡 提示: 如果是自定义损失函数问题，请确保模型训练时使用的损失函数与此处定义一致")
        return
    
    # 加载归一化参数
    norm_params_path = os.path.join(os.path.dirname(args.model_path), 'temp_norm_params.npy')
    norm_params = None
    if os.path.exists(norm_params_path):
        norm_params = np.load(norm_params_path, allow_pickle=True).item()
        print(f"✅ 加载归一化参数: {norm_params.get('method', 'unknown')}")
    else:
        print("⚠️  未找到归一化参数文件，将使用原始数据")
    
    # 设置保存路径
    if args.save_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = os.path.join(os.path.dirname(args.data_dir), 'results')
        os.makedirs(results_dir, exist_ok=True)
        args.save_path = os.path.join(results_dir, 
                                    f'temporal_pred_{args.line_type}_{args.line_index}_{timestamp}.png')
    
    # 绘制时间步预测对比图
    try:
        result = plot_temporal_gt_pred_comparison(
            args.data_dir, model, norm_params, args.time_steps,
            args.line_type, args.line_index, args.save_path, args.downsample_factor
        )
        
        if result:
            gt_lines_data, pred_lines_data, valid_steps = result
            print(f"\n📊 成功绘制 {len(valid_steps)} 个时间步的预测对比图")
            print(f"有效时间步: {valid_steps}")
            
            # 保存数据到文件
            data_save_path = args.save_path.replace('.png', '_data.npz')
            np.savez(data_save_path, 
                    time_steps=valid_steps,
                    ground_truth=np.array(gt_lines_data),
                    prediction=np.array(pred_lines_data),
                    line_type=args.line_type,
                    line_index=args.line_index)
            print(f"💾 时间步预测数据已保存到: {data_save_path}")
        
    except Exception as e:
        print(f"❌ 绘制时间步预测对比图时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
