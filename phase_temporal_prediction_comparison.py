#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
相场时间步预测对比可视化工具
基于温度场版本修改，适配相场数据特点
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
import argparse
from datetime import datetime
import tensorflow as tf
from tensorflow.keras.losses import MeanSquaredError, MeanAbsoluteError, Huber

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

# 定义自定义损失函数 - 与train_phase.py中相同
def combined_loss(y_true, y_pred):
    """
    组合损失函数: 0.6*MSE + 0.2*MAE + 0.2*Huber
    """
    y_true = tf.cast(y_true, tf.float32)
    y_pred = tf.cast(y_pred, tf.float32)
    mse = MeanSquaredError()(y_true, y_pred)
    mae = MeanAbsoluteError()(y_true, y_pred)
    huber = Huber(delta=1.0)(y_true, y_pred)
    return 0.6 * mse + 0.2 * mae + 0.2 * huber

def load_phase_data_single_step(data_dir, step):
    """加载单个时间步的相场数据"""
    file_path = os.path.join(data_dir, f"相场第{step}步.csv")
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    data = pd.read_csv(file_path, header=None, encoding='utf-8').values.astype(np.float32)
    return data

def extract_line_data(data, line_type, line_index):
    """从2D数据中提取指定行或列的数据"""
    if line_type == 'row':
        if line_index >= data.shape[0]:
            raise ValueError(f"行索引 {line_index} 超出数据范围 [0, {data.shape[0]-1}]")
        return data[line_index, :]
    elif line_type == 'col':
        if line_index >= data.shape[1]:
            raise ValueError(f"列索引 {line_index} 超出数据范围 [0, {data.shape[1]-1}]")
        return data[:, line_index]
    else:
        raise ValueError("line_type 必须是 'row' 或 'col'")

def predict_phase_with_model(model, input_data, norm_params=None, downsample_factor=4):
    """使用模型预测相场 - 支持降采样"""
    try:
        print(f"开始预测，输入数据形状: {input_data.shape}")
        
        # 归一化输入
        if norm_params is not None:
            if norm_params['method'] == 'zscore':
                normalized_input = (input_data - norm_params['mean']) / (norm_params['std'] + 1e-10)
            elif norm_params['method'] == 'minmax':
                normalized_input = (input_data - norm_params['min']) / (norm_params['max'] - norm_params['min'] + 1e-10)
            else:
                normalized_input = input_data
        else:
            normalized_input = input_data
        
        # 降采样到模型期望的尺寸
        if downsample_factor > 1:
            try:
                from scipy.ndimage import zoom
                zoom_factor = 1.0 / downsample_factor
                downsampled_input = zoom(normalized_input, zoom_factor, order=3)
                print(f"降采样后形状: {downsampled_input.shape}")
            except Exception as e:
                print(f"降采样失败: {str(e)}，使用简单步长降采样")
                downsampled_input = normalized_input[::downsample_factor, ::downsample_factor]
                print(f"简单降采样后形状: {downsampled_input.shape}")
        else:
            downsampled_input = normalized_input
        
        # 重塑为模型输入格式
        model_input = downsampled_input.reshape(1, downsampled_input.shape[0], downsampled_input.shape[1], 1)
        print(f"模型输入形状: {model_input.shape}")
        
        # 预测
        prediction = model.predict(model_input, verbose=0)
        print(f"模型输出形状: {prediction.shape}")
        
        # 重塑预测结果
        pred_shape = downsampled_input.shape
        prediction_reshaped = prediction.reshape(pred_shape)
        
        # 上采样回原始分辨率
        if downsample_factor > 1:
            try:
                from scipy.ndimage import zoom
                zoom_factor = downsample_factor
                prediction_upsampled = zoom(prediction_reshaped.astype(np.float64), zoom_factor, order=1)
                
                # 确保输出尺寸与输入一致
                if prediction_upsampled.shape != input_data.shape:
                    zoom_factor_x = input_data.shape[0] / prediction_upsampled.shape[0]
                    zoom_factor_y = input_data.shape[1] / prediction_upsampled.shape[1]
                    prediction_upsampled = zoom(prediction_upsampled, (zoom_factor_x, zoom_factor_y), order=1)
                
                prediction_upsampled = prediction_upsampled.astype(np.float32)
                print(f"上采样后形状: {prediction_upsampled.shape}")
            except Exception as e:
                print(f"上采样失败: {str(e)}")
                prediction_upsampled = prediction_reshaped
        else:
            prediction_upsampled = prediction_reshaped
        
        # 反归一化
        if norm_params is not None:
            if norm_params['method'] == 'zscore':
                final_prediction = prediction_upsampled * norm_params['std'] + norm_params['mean']
            elif norm_params['method'] == 'minmax':
                final_prediction = prediction_upsampled * (norm_params['max'] - norm_params['min']) + norm_params['min']
            else:
                final_prediction = prediction_upsampled
        else:
            final_prediction = prediction_upsampled
        
        print(f"最终预测结果形状: {final_prediction.shape}")
        return final_prediction
        
    except Exception as e:
        print(f"预测失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def plot_temporal_gt_pred_comparison(phase_data_dir, model, norm_params, time_steps, 
                                   line_type='row', line_index=400, save_path=None, downsample_factor=4):
    """
    绘制不同时间步同一行/列的Ground Truth和Prediction对比
    """
    # 创建美观的图形 - 单个图表显示GT和Prediction对比
    fig, ax = plt.subplots(figsize=(16, 10), facecolor='white')
    ax.set_facecolor('white')
    
    # 解析时间步
    if isinstance(time_steps, str):
        if ',' in time_steps:
            steps = [int(x.strip()) for x in time_steps.split(',')]
        elif '-' in time_steps:
            start, end = map(int, time_steps.split('-'))
            steps = list(range(start, end + 1, max(1, (end - start) // 10)))
        else:
            steps = [int(time_steps)]
    else:
        steps = time_steps if isinstance(time_steps, list) else [time_steps]
    
    # 设置颜色
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    gt_lines_data = []
    pred_lines_data = []
    valid_steps = []
    
    for i, step in enumerate(steps):
        try:
            print(f"\n🔄 处理第{step}步...")
            
            # 加载Ground Truth数据
            gt_data = load_phase_data_single_step(phase_data_dir, step)
            
            # 使用前一步作为输入进行预测
            if step > 0:
                input_data = load_phase_data_single_step(phase_data_dir, step - 1)
                # 添加降采样因子参数 - 与训练时保持一致
                pred_data = predict_phase_with_model(model, input_data, norm_params, downsample_factor)
                
                if pred_data is None:
                    print(f"⚠️  第{step}步预测失败，跳过")
                    continue
            else:
                print(f"⚠️  第{step}步是起始步，无法预测，跳过")
                continue
            
            # 提取线数据
            gt_line = extract_line_data(gt_data, line_type, line_index)
            pred_line = extract_line_data(pred_data, line_type, line_index)
            x_coords = np.arange(len(gt_line))
            
            color = colors[i % len(colors)]
            line_label = f'Step {step}'
            
            # 在同一图表中绘制Ground Truth和Prediction
            # Ground Truth (实线)
            ax.plot(x_coords, gt_line, color=color, linewidth=2.5, 
                   label=f'GT {line_label}', alpha=0.9, linestyle='-')
            
            # Prediction (虚线)
            ax.plot(x_coords, pred_line, color=color, linewidth=2.5, 
                   label=f'Pred {line_label}', alpha=0.9, linestyle='--')
            
            gt_lines_data.append(gt_line)
            pred_lines_data.append(pred_line)
            valid_steps.append(step)
            
            print(f"✅ 第{step}步: GT范围[{gt_line.min():.4f}, {gt_line.max():.4f}], "
                  f"Pred范围[{pred_line.min():.4f}, {pred_line.max():.4f}]")
            
        except Exception as e:
            print(f"⚠️  跳过第{step}步: {str(e)}")
            continue
    
    if not gt_lines_data:
        print("❌ 没有有效的数据可以绘制")
        return None
    
    # 设置图形属性
    axis_label = 'X' if line_type == 'row' else 'Y'
    line_label = f'Row {line_index}' if line_type == 'row' else f'Col {line_index}'
    
    # 设置单个图表样式
    ax.set_xlabel(f'{axis_label} Coordinate', fontsize=12, fontweight='bold')
    ax.set_ylabel('Phase Fraction', fontsize=12, fontweight='bold')
    ax.set_title(f'Phase Field GT vs Prediction - {line_label} Temporal Evolution', 
                fontsize=14, fontweight='bold', pad=15)
    
    # 优化图例显示 - 将GT和Pred成对显示
    handles, labels = ax.get_legend_handles_labels()
    # 重新排序图例，让同一时间步的GT和Pred相邻显示
    reordered_handles = []
    reordered_labels = []
    n_steps = len(valid_steps)
    for i in range(n_steps):
        # GT在前，Pred在后
        gt_idx = i * 2
        pred_idx = i * 2 + 1
        if gt_idx < len(handles):
            reordered_handles.append(handles[gt_idx])
            reordered_labels.append(labels[gt_idx])
        if pred_idx < len(handles):
            reordered_handles.append(handles[pred_idx])
            reordered_labels.append(labels[pred_idx])
    
    ax.legend(reordered_handles, reordered_labels, fontsize=10, frameon=True, 
             fancybox=True, shadow=True, loc='upper right', 
             bbox_to_anchor=(0.98, 0.98), ncol=2)
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 设置坐标轴样式
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(0.8)
    ax.spines['bottom'].set_linewidth(0.8)
    
    # 计算统计信息
    all_gt_data = np.concatenate(gt_lines_data)
    all_pred_data = np.concatenate(pred_lines_data)
    
    # 计算误差统计
    errors = []
    for gt_line, pred_line in zip(gt_lines_data, pred_lines_data):
        error = np.mean(np.abs(gt_line - pred_line))
        errors.append(error)
    
    mean_error = np.mean(errors)
    max_error = np.max(errors)
    
    # 添加统计信息框
    stats_text = f'Time Steps: {len(valid_steps)}\n' \
                f'Step Range: {valid_steps[0]} → {valid_steps[-1]}\n' \
                f'Mean MAE: {mean_error:.6f}\n' \
                f'Max MAE: {max_error:.6f}\n' \
                f'GT Range: [{all_gt_data.min():.4f}, {all_gt_data.max():.4f}]\n' \
                f'Pred Range: [{all_pred_data.min():.4f}, {all_pred_data.max():.4f}]'
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
           verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5',
           facecolor='lightcyan', alpha=0.8, edgecolor='teal', linewidth=1))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"📊 图表已保存到: {save_path}")
    
    plt.show()
    return gt_lines_data, pred_lines_data, valid_steps

def find_available_phase_model():
    """自动查找可用的相场模型文件"""
    model_candidates = [
        './models/phi_model.keras',
        './models/phase_model.keras',
        './models/phi_model_checkpoint.keras'
    ]

    for model_path in model_candidates:
        if os.path.exists(model_path):
            return model_path

    return None

def interactive_phase_temporal_pred_console():
    """交互式相场时间步预测对比控制台"""
    print("🎯 " + "="*60)
    print("    相场时间步预测对比可视化工具")
    print("="*64)

    # 获取降采样因子
    default_downsample = 4
    while True:
        downsample_input = input(f"🔍 请输入降采样因子 (默认: {default_downsample}): ").strip()
        if not downsample_input:
            downsample_factor = default_downsample
            break
        try:
            downsample_factor = int(downsample_input)
            if downsample_factor < 1:
                print("❌ 降采样因子必须大于等于1")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的整数")

    # 获取数据目录
    default_data_dir = 'E:/Freezedata/PhaseField_Data/'
    data_dir = input(f"📂 请输入相场数据目录路径 (默认: {default_data_dir}): ").strip()
    if not data_dir:
        data_dir = default_data_dir

    if not os.path.exists(data_dir):
        print(f"❌ 目录不存在: {data_dir}")
        return None

    # 获取模型路径
    default_model = find_available_phase_model()
    if default_model:
        print(f"🤖 找到可用模型: {default_model}")
        model_input = input(f"请输入模型路径 (默认: {default_model}): ").strip()
        model_path = model_input if model_input else default_model
    else:
        model_path = input("🤖 请输入相场模型路径: ").strip()
        if not model_path:
            print("❌ 必须提供模型路径")
            return None

    if not os.path.exists(model_path):
        print(f"❌ 模型文件不存在: {model_path}")
        return None

    # 选择对比类型
    print("\n📏 选择对比类型:")
    print("  1. Row (行对比)")
    print("  2. Column (列对比)")

    while True:
        type_choice = input("请选择类型 (1/2, 默认1): ").strip()
        if type_choice == '2':
            line_type = 'col'
            break
        elif type_choice == '1' or type_choice == '':
            line_type = 'row'
            break
        else:
            print("❌ 请输入 1 或 2")

    # 获取行/列索引
    while True:
        index_input = input(f"📍 请输入要分析的{line_type}索引 (默认: 400): ").strip()
        if not index_input:
            line_index = 400
            break
        try:
            line_index = int(index_input)
            if line_index < 0:
                print("❌ 索引必须为非负整数")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的整数")

    # 获取时间步
    print("\n⏰ 时间步设置:")
    print("  支持格式: 单个数字(如: 2000), 逗号分隔(如: 500,1000,1500,2000), 范围(如: 500-2000)")

    time_steps_input = input("请输入时间步 (默认: 500,1000,1500,2000): ").strip()
    if not time_steps_input:
        time_steps = "500,1000,1500,2000"
    else:
        time_steps = time_steps_input

    # 设置保存路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_dir = os.path.join(os.path.dirname(data_dir), 'results')
    os.makedirs(results_dir, exist_ok=True)
    save_path = os.path.join(results_dir,
                            f'phase_temporal_pred_{line_type}_{line_index}_{timestamp}.png')

    print("\n" + "="*64)
    print("📋 配置摘要:")
    print(f"   数据目录: {data_dir}")
    print(f"   模型路径: {model_path}")
    print(f"   降采样因子: {downsample_factor}")
    print(f"   对比类型: {line_type}")
    print(f"   索引: {line_index}")
    print(f"   时间步: {time_steps}")
    print(f"   保存路径: {save_path}")
    print("="*64)

    return {
        'data_dir': data_dir,
        'model_path': model_path,
        'line_type': line_type,
        'line_index': line_index,
        'time_steps': time_steps,
        'save_path': save_path,
        'downsample_factor': downsample_factor
    }

def main():
    parser = argparse.ArgumentParser(description='相场时间步预测对比可视化工具')
    parser.add_argument('-i', '--interactive', action='store_true',
                       help='启用交互式模式')
    parser.add_argument('--data_dir', type=str, default='E:/Freezedata/PhaseField_Data/',
                       help='相场数据目录路径')
    parser.add_argument('--model_path', type=str, default=None,
                       help='相场模型路径')
    parser.add_argument('--line_type', type=str, choices=['row', 'col'], default='row',
                       help='对比类型: row 或 col')
    parser.add_argument('--line_index', type=int, default=100,
                       help='要分析的行/列索引')
    parser.add_argument('--time_steps', type=str, default='500,1000,2000,3000',
                       help='时间步，支持逗号分隔或范围格式')
    parser.add_argument('--downsample_factor', type=int, default=4,
                       help='降采样因子，应与训练时保持一致')
    parser.add_argument('--save_path', type=str, default='./models/results/1-temp/1-phase_profile.png',
                       help='图片保存路径')

    args = parser.parse_args()

    # 交互式模式
    if args.interactive:
        config = interactive_phase_temporal_pred_console()
        if config is None:
            return

        args.data_dir = config['data_dir']
        args.model_path = config['model_path']
        args.line_type = config['line_type']
        args.line_index = config['line_index']
        args.time_steps = config['time_steps']
        args.save_path = config['save_path']
        args.downsample_factor = config['downsample_factor']

    # 自动查找模型
    if args.model_path is None:
        args.model_path = find_available_phase_model()
        if args.model_path is None:
            print("❌ 未找到可用的相场模型文件")
            print("请确保以下位置存在模型文件:")
            print("  - ./models/phi_model.keras")
            print("  - ./models/phase_model.keras")
            print("  - ./models/phi_model_checkpoint.keras")
            return

    # 加载模型 - 包含自定义损失函数
    try:
        print(f"📂 加载模型: {args.model_path}")

        # 定义自定义对象字典
        custom_objects = {
            'combined_loss': combined_loss
        }

        model = tf.keras.models.load_model(args.model_path, custom_objects=custom_objects)
        print("✅ 模型加载成功")
    except Exception as e:
        print(f"❌ 模型加载失败: {str(e)}")
        print("💡 提示: 如果是自定义损失函数问题，请确保模型训练时使用的损失函数与此处定义一致")
        return

    # 加载归一化参数
    norm_params_path = os.path.join(os.path.dirname(args.model_path), 'phi_norm_params.npy')
    if os.path.exists(norm_params_path):
        norm_params = np.load(norm_params_path, allow_pickle=True).item()
        print(f"📊 加载归一化参数: {norm_params}")
    else:
        print("⚠️  未找到归一化参数文件，将不进行归一化")
        norm_params = None

    # 设置保存路径
    if args.save_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_dir = os.path.join(os.path.dirname(args.data_dir), 'results')
        os.makedirs(results_dir, exist_ok=True)
        args.save_path = os.path.join(results_dir,
                                    f'phase_temporal_pred_{args.line_type}_{args.line_index}_{timestamp}.png')

    # 绘制时间步预测对比图
    try:
        result = plot_temporal_gt_pred_comparison(
            args.data_dir, model, norm_params, args.time_steps,
            args.line_type, args.line_index, args.save_path, args.downsample_factor
        )

        if result:
            gt_lines_data, pred_lines_data, valid_steps = result
            print(f"\n📊 成功绘制 {len(valid_steps)} 个时间步的GT vs Prediction对比图")

            # 计算总体统计
            all_gt = np.concatenate(gt_lines_data)
            all_pred = np.concatenate(pred_lines_data)
            overall_mae = np.mean(np.abs(all_gt - all_pred))

            print(f"📈 总体统计:")
            print(f"   时间步范围: {valid_steps[0]} → {valid_steps[-1]}")
            print(f"   总体MAE: {overall_mae:.6f}")
            print(f"   GT相分数范围: [{all_gt.min():.4f}, {all_gt.max():.4f}]")
            print(f"   Pred相分数范围: [{all_pred.min():.4f}, {all_pred.max():.4f}]")

    except Exception as e:
        print(f"❌ 绘制时间步预测对比图时出错: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
