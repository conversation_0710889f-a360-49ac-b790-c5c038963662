import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt
import os
import pandas as pd
import time
import psutil
import GPUtil
from datetime import datetime
import gc
import json
import pickle

# 配置matplotlib支持中文
try:
    plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans', 'Arial Unicode MS', 'sans-serif']
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
    print("已配置matplotlib支持中文显示")
except Exception as e:
    print(f"配置中文字体失败: {str(e)}，将使用英文替代")

    # 中英文字段映射表，用于替换中文
    FIELD_NAME_MAP = {
        "相场": "Phase Field",
        "温度场": "Temperature Field",
        "溶质场": "Concentration Field",
        "相场误差分析": "Phase Field Error Analysis",
        "温度场误差分析": "Temperature Field Error Analysis",
        "溶质场误差分析": "Concentration Field Error Analysis"
    }

# ================== 通用工具函数 ==================

def clear_memory():
    gc.collect()
    if tf.config.list_physical_devices('GPU'):
        try:
            tf.keras.backend.clear_session()
        except Exception as e:
            print(f"清理GPU内存时出错: {str(e)}")
    memory = psutil.virtual_memory()
    print(f"内存清理后使用率: {memory.percent}%")
    print(f"可用内存: {memory.available / 1024 / 1024 / 1024:.2f} GB")

def setup_gpu():
    print("TensorFlow版本:", tf.__version__)
    print("\nCUDA配置:")
    print("CUDA是否可用:", tf.test.is_built_with_cuda())
    gpus = tf.config.list_physical_devices('GPU')
    if gpus:
        try:
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print("\nGPU配置:")
            print(f"- 检测到 {len(gpus)} 个GPU设备")
            print("- GPU显存已设置为按需分配")
            for gpu in gpus:
                memory_limit = int(get_gpu_memory(gpu) * 0.8)
                print(f"- 将GPU {gpu} 内存限制设置为 {memory_limit} MB")
                tf.config.set_logical_device_configuration(
                    gpu,
                    [tf.config.LogicalDeviceConfiguration(memory_limit=memory_limit)]
                )
            tf.config.set_visible_devices(gpus[0], 'GPU')
            logical_gpus = tf.config.list_logical_devices('GPU')
            print(f"- 可用的逻辑GPU数量: {len(logical_gpus)}")
            print("\n执行GPU测试计算...")
            with tf.device('/GPU:0'):
                a = tf.random.normal([1000, 1000])
                b = tf.random.normal([1000, 1000])
                c = tf.matmul(a, b)
            print("GPU测试完成：矩阵乘法运算成功")
            return True
        except RuntimeError as e:
            print("\nGPU配置错误:", str(e))
    else:
        print("\n未检测到GPU，请确保：")
        print("1. 已安装NVIDIA GPU硬件")
        print("2. 已安装最新的NVIDIA驱动程序")
        print("3. 已安装CUDA Toolkit（建议11.8版本）")
        print("4. 已安装cuDNN（与CUDA版本匹配）")
        print("5. 已安装tensorflow-gpu或正确版本的tensorflow")
        print("\n您可以访问以下链接获取安装指南：")
        print("- NVIDIA驱动：https://www.nvidia.com/Download/index.aspx")
        print("- CUDA Toolkit：https://developer.nvidia.com/cuda-toolkit")
        print("- cuDNN：https://developer.nvidia.com/cudnn")
        return False

def setup_mixed_precision():
    if tf.config.list_physical_devices('GPU'):
        try:
            tf.keras.mixed_precision.set_global_policy('mixed_float16')
            print("\n混合精度配置:")
            print("- 已启用mixed_float16策略")
            return True
        except Exception as e:
            print("\n设置混合精度失败:", str(e))
            return False
    return False

def get_gpu_memory(gpu_device):
    try:
        gpu_details = tf.config.experimental.get_device_details(gpu_device)
        return gpu_details['device_memory_size'] // (1024 * 1024)
    except:
        try:
            gpus = GPUtil.getGPUs()
            for gpu in gpus:
                if gpu_device.name.endswith(str(gpu.id)):
                    return gpu.memoryTotal
        except:
            pass
    return 4096

def load_phase_data(phi_data_dir, start_step=0, end_step=3050, stride=1, max_samples=100):
    phi_data = []
    steps = list(range(start_step, end_step + 1, stride))
    if len(steps) > max_samples:
        sample_stride = len(steps) // max_samples + 1
        steps = steps[::sample_stride]
    for i in steps:
        phi_file = os.path.join(phi_data_dir, f"相场第{i}步.csv")
        if not os.path.exists(phi_file):
            print(f"警告: 文件不存在 {phi_file}")
            continue
        phi = pd.read_csv(phi_file, header=None, encoding='utf-8').values.astype(np.float32)
        phi_data.append(phi)
    if not phi_data:
        raise ValueError("未能加载任何相场数据，请检查文件路径和格式")
    return np.array(phi_data, dtype=np.float32)

def load_temp_data(temp_data_dir, start_step=0, end_step=3050, stride=1, max_samples=100):
    temp_data = []
    steps = list(range(start_step, end_step + 1, stride))
    if len(steps) > max_samples:
        sample_stride = len(steps) // max_samples + 1
        steps = steps[::sample_stride]
    for i in steps:
        temp_file = os.path.join(temp_data_dir, f"温度场第{i}步.csv")
        if not os.path.exists(temp_file):
            print(f"警告: 文件不存在 {temp_file}")
            continue
        temp = pd.read_csv(temp_file, header=None, encoding='utf-8').values.astype(np.float32)
        temp_data.append(temp)
    if not temp_data:
        raise ValueError("未能加载任何温度场数据，请检查文件路径和格式")
    return np.array(temp_data, dtype=np.float32)

def load_solute_data(solute_data_dir, start_step=0, end_step=3050, stride=1, max_samples=100):
    solute_data = []
    steps = list(range(start_step, end_step + 1, stride))
    if len(steps) > max_samples:
        sample_stride = len(steps) // max_samples + 1
        steps = steps[::sample_stride]
    for i in steps:
        solute_file = os.path.join(solute_data_dir, f"溶质场第{i}步.csv")
        if not os.path.exists(solute_file):
            print(f"警告: 文件不存在 {solute_file}")
            continue
        solute = pd.read_csv(solute_file, header=None, encoding='utf-8').values.astype(np.float32)
        solute_data.append(solute)
    if not solute_data:
        raise ValueError("未能加载任何溶质场数据，请检查文件路径和格式")
    return np.array(solute_data, dtype=np.float32)

def load_freeze_data(phi_data_dir, temp_data_dir, solute_data_dir, start_step=0, end_step=3050, stride=1, max_samples=100):
    phi_data = []
    temp_data = []
    solute_data = []
    steps = list(range(start_step, end_step + 1, stride))
    if len(steps) > max_samples:
        sample_stride = len(steps) // max_samples + 1
        steps = steps[::sample_stride]
        print(f"样本数量超过最大限制({max_samples})，进一步增加采样步长为: {stride * sample_stride}")
    print(f"将加载 {len(steps)} 个样本，采样步长为 {stride}")
    for i in steps:
        try:
            phi_file = os.path.join(phi_data_dir, f"相场第{i}步.csv")
            temp_file = os.path.join(temp_data_dir, f"温度场第{i}步.csv")
            solute_file = os.path.join(solute_data_dir, f"溶质场第{i}步.csv")
            if not os.path.exists(phi_file):
                print(f"警告: 文件不存在 {phi_file}")
                continue
            if not os.path.exists(temp_file):
                print(f"警告: 文件不存在 {temp_file}")
                continue
            if not os.path.exists(solute_file):
                print(f"警告: 文件不存在 {solute_file}")
                continue
            phi = pd.read_csv(phi_file, header=None, encoding='utf-8').values.astype(np.float32)
            temp = pd.read_csv(temp_file, header=None, encoding='utf-8').values.astype(np.float32)
            solute = pd.read_csv(solute_file, header=None, encoding='utf-8').values.astype(np.float32)
            print(f"成功加载第{i}步数据: 相场形状{phi.shape}, 温度场形状{temp.shape}, 溶质场形状{solute.shape}")
            phi_data.append(phi)
            temp_data.append(temp)
            solute_data.append(solute)
            if len(phi_data) % 10 == 0:
                memory = psutil.virtual_memory()
                print(f"已加载 {len(phi_data)} 个样本，当前内存使用率: {memory.percent}%")
                if memory.percent > 80:
                    print("内存使用率过高，尝试清理内存...")
                    clear_memory()
        except Exception as e:
            print(f"加载第{i}步数据时出错: {str(e)}")
    if not phi_data or not temp_data or not solute_data:
        raise ValueError("未能加载任何数据，请检查文件路径和格式")
    print(f"数据加载完成，共加载 {len(phi_data)} 个样本")
    return np.array(phi_data, dtype=np.float32), np.array(temp_data, dtype=np.float32), np.array(solute_data, dtype=np.float32)

# ================== 监控和回调类 ==================

class GPUMonitor(tf.keras.callbacks.Callback):
    def __init__(self, enabled=True):
        super(GPUMonitor, self).__init__()
        self.enabled = enabled
        self.start_time = None
        self.epoch_times = []
        self.batch_times = []
        self.last_batch_time = None

    def on_epoch_begin(self, epoch, logs=None):
        if not self.enabled:
            return
        self.start_time = time.time()
        print(f"\n轮次 {epoch + 1} 开始...")
        self._print_gpu_stats()

    def on_epoch_end(self, epoch, logs=None):
        if not self.enabled:
            return
        end_time = time.time()
        epoch_time = end_time - self.start_time
        self.epoch_times.append(epoch_time)
        avg_batch_time = sum(self.batch_times) / len(self.batch_times) if self.batch_times else 0
        self.batch_times = []
        print(f"\n轮次 {epoch + 1} 完成:")
        print(f"- 本轮训练时间: {epoch_time:.2f} 秒")
        print(f"- 平均批次时间: {avg_batch_time:.4f} 秒")
        print(f"- 当前损失: {logs.get('loss', 0):.6f}")
        self._print_gpu_stats()

    def on_train_batch_end(self, batch, logs=None):
        if not self.enabled:
            return
        current_time = time.time()
        if self.last_batch_time is not None:
            batch_time = current_time - self.last_batch_time
            self.batch_times.append(batch_time)
        self.last_batch_time = current_time

    def _print_gpu_stats(self):
        try:
            gpus = GPUtil.getGPUs()
            for gpu in gpus:
                print(f"\nGPU {gpu.id} 状态:")
                print(f"- 显存使用: {gpu.memoryUsed}/{gpu.memoryTotal} MB ({gpu.memoryUtil * 100:.1f}%)")
                print(f"- GPU利用率: {gpu.load * 100:.1f}%")
                print(f"- 温度: {gpu.temperature}°C")
        except Exception as e:
            print(f"无法获取GPU信息: {str(e)}")
        memory = psutil.virtual_memory()
        print(f"\n系统内存使用:")
        print(f"- 已用: {memory.used / 1024 / 1024 / 1024:.1f}GB")
        print(f"- 总量: {memory.total / 1024 / 1024 / 1024:.1f}GB")
        print(f"- 使用率: {memory.percent}%")


class TempFieldMonitor(tf.keras.callbacks.Callback):
    """监控温度场训练过程中的异常值和梯度问题"""
    def __init__(self, model_type='temp', enabled=True):
        super(TempFieldMonitor, self).__init__()
        self.model_type = model_type
        self.enabled = enabled
        self.loss_history = []

    def on_epoch_begin(self, epoch, logs=None):
        if not self.enabled:
            return
        print(f"\n开始监控{self.model_type}模型第{epoch + 1}轮训练...")

    def on_epoch_end(self, epoch, logs=None):
        if not self.enabled:
            return
        current_loss = logs.get('loss')
        self.loss_history.append(current_loss)
        if len(self.loss_history) > 1:
            loss_change = abs(self.loss_history[-1] - self.loss_history[-2])
            if loss_change > 100:
                print(f"\n⚠️ 警告: 检测到损失突变! 变化量: {loss_change:.2f}")
            elif current_loss > 1000:
                print(f"\n⚠️ 警告: 损失值异常高! 当前损失: {current_loss:.2f}")
        print(f"\n{self.model_type}模型第{epoch + 1}轮监控:")
        print(f"- 当前损失: {current_loss:.6f}")
        if len(self.loss_history) > 5:
            recent_trend = sum(self.loss_history[-5:]) / 5
            print(f"- 最近5轮平均损失: {recent_trend:.6f}")
            if all(abs(self.loss_history[-i] - self.loss_history[-i - 1]) < 1e-5 for i in range(1, 5)):
                print("⚠️ 警告: 训练可能已停滞，建议调整学习率或模型结构")

    def on_train_batch_end(self, batch, logs=None):
        if not self.enabled or batch % 10 != 0:
            return
        batch_loss = logs.get('loss')
        if batch_loss is not None and batch_loss > 1000:
            print(f"\n⚠️ 批次{batch}损失异常: {batch_loss:.2f}")


# ================== 数据处理函数 ==================

def prepare_training_data(fields, dx=1e-7, dy=1e-7, downsample_factor=1, field_type='phase', use_data_augmentation=True):
    # 检查数据形状
    if len(fields) < 2:
        raise ValueError("至少需要两个时间步的数据来创建输入-输出对")

    print(f"准备{field_type}数据: 有{len(fields)}个时间步，每个场的形状为{fields[0].shape}")

    # 如果是温度场数据，进行改进的归一化处理
    if field_type == 'temp':
        print("对温度场数据进行改进的归一化处理...")
        all_data = np.concatenate([field.flatten() for field in fields])
        global_min = np.min(all_data)
        global_max = np.max(all_data)
        global_mean = np.mean(all_data)
        global_std = np.std(all_data)
        print(f"温度场数据统计: 最小值 {global_min}, 最大值 {global_max}, 均值 {global_mean}, 标准差 {global_std}")
        q1 = np.percentile(all_data, 1)
        q99 = np.percentile(all_data, 99)
        iqr_range = q99 - q1
        lower_bound = q1 - 1.5 * iqr_range
        upper_bound = q99 + 1.5 * iqr_range
        print(f"异常值处理范围: 下界 {lower_bound}, 上界 {upper_bound}")
        fields = [(field - global_mean) / (global_std + 1e-10) for field in fields]
        print("温度场Z-score归一化完成")
        norm_params = {
            'min': global_min,
            'max': global_max,
            'mean': global_mean,
            'std': global_std,
            'lower_bound': lower_bound,
            'upper_bound': upper_bound
        }
        del all_data
        import gc
        gc.collect()
    else:
        norm_params = None

    if downsample_factor > 1:
        downsampled_fields = []
        for field in fields:
            try:
                from scipy.ndimage import zoom
                zoom_factor = 1.0 / downsample_factor
                downsampled = zoom(field, zoom_factor, order=3)
                downsampled_fields.append(downsampled)
            except Exception as e:
                print(f"双三次插值降采样失败: {str(e)}，使用简单步长降采样")
                downsampled = field[::downsample_factor, ::downsample_factor]
                downsampled_fields.append(downsampled)
        fields = downsampled_fields
        print(f"已对场数据进行 {downsample_factor}倍 降采样，新形状为 {fields[0].shape}")
        del downsampled_fields
        import gc
        gc.collect()

    m, n = fields[0].shape
    x = np.linspace(0, m * dx, m)
    y = np.linspace(0, n * dy, n)
    X, Y = np.meshgrid(x, y)
    coords = np.stack([X.flatten(), Y.flatten()], axis=-1)

    X_train = []
    y_train = []

    for i in range(len(fields) - 1):
        input_field = fields[i].reshape(-1, fields[i].shape[0], fields[i].shape[1], 1)
        X_train.append(input_field)
        y_train.append(fields[i + 1].flatten())
        if use_data_augmentation:
            flipped_h = np.flip(input_field, axis=2)
            X_train.append(flipped_h)
            y_train.append(np.flip(fields[i + 1], axis=1).flatten())
            flipped_v = np.flip(input_field, axis=1)
            X_train.append(flipped_v)
            y_train.append(np.flip(fields[i + 1], axis=0).flatten())
            rotated = np.rot90(input_field.squeeze(0), k=1).reshape(input_field.shape)
            X_train.append(rotated)
            y_train.append(np.rot90(fields[i + 1], k=1).flatten())
            if field_type == 'phase':
                noise_level = 0.02
            elif field_type == 'temp':
                noise_level = 0.01
            else:
                noise_level = 0.005
            noisy = input_field + np.random.normal(0, noise_level, input_field.shape)
            X_train.append(noisy)
            y_train.append(fields[i + 1].flatten())
            if field_type == 'temp':
                scale_factors = [0.95, 0.975, 1.025, 1.05]
                for scale in scale_factors:
                    try:
                        from scipy.ndimage import zoom
                        scaled = zoom(input_field.squeeze(0), (scale, scale, 1), order=1)
                        if scaled.shape[0] != input_field.shape[1] or scaled.shape[1] != input_field.shape[2]:
                            scaled = zoom(scaled, (input_field.shape[1] / scaled.shape[0],
                                                   input_field.shape[2] / scaled.shape[1], 1), order=1)
                        X_train.append(scaled.reshape(input_field.shape))
                        scaled_output = zoom(fields[i + 1], (scale, scale), order=1)
                        if scaled_output.shape[0] != fields[i + 1].shape[0] or scaled_output.shape[1] != \
                                fields[i + 1].shape[1]:
                            scaled_output = zoom(scaled_output, (fields[i + 1].shape[0] / scaled_output.shape[0],
                                                                 fields[i + 1].shape[1] / scaled_output.shape[1]),
                                                 order=1)
                        y_train.append(scaled_output.flatten())
                    except Exception as e:
                        print(f"缩放增强失败: {str(e)}")
        if i % 10 == 0 and i > 0:
            import gc
            gc.collect()

    print(f"准备完成: 生成了{len(X_train)}个训练样本")
    X_branch = np.array(X_train, dtype=np.float32)
    y_train = np.array(y_train, dtype=np.float32)
    del X_train
    del fields
    import gc
    gc.collect()
    if len(X_branch.shape) > 4:
        X_branch = X_branch.squeeze(1)
    print(f"X_branch shape: {X_branch.shape}")
    print(f"y_train shape: {y_train.shape}")
    memory = __import__('psutil').virtual_memory()
    print(f"训练数据准备完成后内存使用率: {memory.percent}%")
    X_size = X_branch.nbytes / (1024 * 1024)
    y_size = y_train.nbytes / (1024 * 1024)
    print(f"X_branch 内存占用: {X_size:.2f} MB")
    print(f"y_train 内存占用: {y_size:.2f} MB")
    return X_branch, coords, y_train, norm_params

def visualize_prediction(true_field, pred_field, field_type, data_dir, reshape_pred=True, save_dir=None):
    """可视化预测结果"""
    # 将预测结果重塑回原始场的形状（如果需要）
    m, n = true_field.shape
    if reshape_pred:
        try:
            pred_field = pred_field.astype(np.float32)
            pred_field_reshaped = pred_field.reshape(m, n)
            print(f"成功将预测结果重塑为 {m}x{n} 的形状")
        except Exception as e:
            print(f"重塑预测结果时出错: {str(e)}")
            try:
                from scipy.ndimage import zoom
                pred_field = pred_field.astype(np.float32)
                if len(pred_field.shape) > 1:
                    zoom_factor = (m / pred_field.shape[0], n / pred_field.shape[1])
                    pred_field_reshaped = zoom(pred_field, zoom_factor, order=1)
                else:
                    side_len = int(np.sqrt(pred_field.shape[0]))
                    if side_len * side_len == pred_field.shape[0]:
                        temp = pred_field.reshape(side_len, side_len)
                        zoom_factor = (m / side_len, n / side_len)
                        pred_field_reshaped = zoom(temp, zoom_factor, order=1)
                    else:
                        pred_field_reshaped = np.zeros((m, n), dtype=np.float32)
                        min_size = min(pred_field.shape[0], m * n)
                        pred_field_reshaped.flat[:min_size] = pred_field[:min_size]
            except Exception as e2:
                print(f"插值调整大小也失败: {str(e2)}")
                pred_field_reshaped = np.zeros((m, n), dtype=np.float32)
                pred_field_flat = pred_field.flatten()
                min_size = min(pred_field_flat.size, m * n)
                pred_field_reshaped.flat[:min_size] = pred_field_flat[:min_size]
    else:
        if pred_field.shape != true_field.shape:
            try:
                from scipy.ndimage import zoom
                pred_field = pred_field.astype(np.float32)
                zoom_factor = (m / pred_field.shape[0], n / pred_field.shape[1])
                pred_field_reshaped = zoom(pred_field, zoom_factor, order=1)
            except Exception as e:
                pred_field_reshaped = np.zeros_like(true_field, dtype=np.float32)
                start_row = (m - pred_field.shape[0]) // 2
                start_col = (n - pred_field.shape[1]) // 2
                end_row = min(start_row + pred_field.shape[0], m)
                end_col = min(start_col + pred_field.shape[1], n)
                pred_height = min(pred_field.shape[0], m - start_row)
                pred_width = min(pred_field.shape[1], n - start_col)
                pred_field_reshaped[start_row:end_row, start_col:end_col] = pred_field[:pred_height, :pred_width]
        else:
            pred_field_reshaped = pred_field

    # 确定保存目录
    if save_dir:
        base_save_dir = save_dir
    else:
        base_save_dir = os.path.dirname(data_dir)

    # 根据场类型创建子目录
    if field_type == "phase field":
        final_save_dir = os.path.join(base_save_dir, "PhaseField_Images")
    elif field_type == "temperature field":
        final_save_dir = os.path.join(base_save_dir, "TemperatureField_Images")
    else:  # concentration field
        final_save_dir = os.path.join(base_save_dir, "ConcentrationField_Images")

    os.makedirs(final_save_dir, exist_ok=True)
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))
    vmin = min(np.min(true_field), np.min(pred_field_reshaped))
    vmax = max(np.max(true_field), np.max(pred_field_reshaped))

    im1 = ax1.imshow(true_field, cmap='jet', vmin=vmin, vmax=vmax)
    ax1.set_title('Ground Truth Field')
    plt.colorbar(im1, ax=ax1)

    im2 = ax2.imshow(pred_field_reshaped, cmap='jet', vmin=vmin, vmax=vmax)
    ax2.set_title('Predicted Field')
    plt.colorbar(im2, ax=ax2)

    field_title_map = {
        "phase field": "Phase Field",
        "temperature field": "Temperature Field",
        "concentration field": "Concentration Field"
    }
    plt.suptitle(field_title_map.get(field_type, field_type))
    save_path = os.path.join(final_save_dir, f'{field_type.replace(" ", "_")}_prediction_{timestamp}.png')
    plt.savefig(save_path)
    print(f"已保存预测结果到: {save_path}")
    plt.close()
    return save_path

def compare_fields(true_field, pred_field, title, save_dir):
    """生成真实场与预测场的比较图，包括差异分析"""
    true_field = true_field.astype(np.float32)
    pred_field = pred_field.astype(np.float32)

    # 确保预测场与真实场形状相同
    m, n = true_field.shape
    try:
        if pred_field.shape != true_field.shape:
            print(f"预测场形状 {pred_field.shape} 与真实场形状 {true_field.shape} 不匹配，尝试调整")
            try:
                from scipy.ndimage import zoom
                zoom_factor = (m / pred_field.shape[0], n / pred_field.shape[1])
                pred_field_reshaped = zoom(pred_field, zoom_factor, order=1)
                print(f"已将预测场调整为与真实场相同形状: {pred_field_reshaped.shape}")
            except Exception as e:
                print(f"插值调整失败: {str(e)}")
                pred_field_reshaped = np.zeros_like(true_field)
                start_row = (m - pred_field.shape[0]) // 2
                start_col = (n - pred_field.shape[1]) // 2
                end_row = min(start_row + pred_field.shape[0], m)
                end_col = min(start_col + pred_field.shape[1], n)
                pred_height = min(pred_field.shape[0], m - start_row)
                pred_width = min(pred_field.shape[1], n - start_col)
                pred_field_reshaped[start_row:end_row, start_col:end_col] = pred_field[:pred_height, :pred_width]
        else:
            pred_field_reshaped = pred_field
    except Exception as e:
        print(f"处理预测场形状时出错: {str(e)}")
        pred_field_reshaped = np.zeros_like(true_field)

    # 计算误差
    error = true_field - pred_field_reshaped
    abs_error = np.abs(error)
    mean_error = np.mean(error)
    rmse = np.sqrt(np.mean(np.square(error)))
    max_error = np.max(abs_error)
    min_error = np.min(abs_error)

    # 创建可视化图表
    fig = plt.figure(figsize=(18, 10))
    vmin = min(np.min(true_field), np.min(pred_field_reshaped))
    vmax = max(np.max(true_field), np.max(pred_field_reshaped))

    # 1. 真实场
    ax1 = fig.add_subplot(2, 2, 1)
    im1 = ax1.imshow(true_field, cmap='jet', vmin=vmin, vmax=vmax)
    ax1.set_title('Ground Truth')
    plt.colorbar(im1, ax=ax1)

    # 2. 预测场
    ax2 = fig.add_subplot(2, 2, 2)
    im2 = ax2.imshow(pred_field_reshaped, cmap='jet', vmin=vmin, vmax=vmax)
    ax2.set_title('Prediction')
    plt.colorbar(im2, ax=ax2)

    # 3. 误差分布
    ax3 = fig.add_subplot(2, 2, 3)
    error_max = max(abs(np.min(error)), abs(np.max(error)))
    im3 = ax3.imshow(error, cmap='coolwarm', vmin=-error_max, vmax=error_max)
    ax3.set_title('Error Distribution (Ground Truth - Prediction)')
    plt.colorbar(im3, ax=ax3)

    # 4. 误差直方图
    ax4 = fig.add_subplot(2, 2, 4)
    ax4.hist(error.flatten(), bins=50, color='skyblue', edgecolor='black', alpha=0.7)
    ax4.axvline(x=0, color='r', linestyle='--')
    ax4.set_title('Error Histogram')
    ax4.set_xlabel('Error Value')
    ax4.set_ylabel('Frequency')

    # 添加统计信息
    stats_text = f"Mean Error: {mean_error:.6f}\nRMSE: {rmse:.6f}\nMax Error: {max_error:.6f}\nMin Error: {min_error:.6f}"
    fig.text(0.02, 0.02, stats_text, fontsize=10, bbox=dict(facecolor='white', alpha=0.8))

    # 设置总标题
    title_map = {
        "相场误差分析": "Phase Field Error Analysis",
        "温度场误差分析": "Temperature Field Error Analysis",
        "溶质场误差分析": "Concentration Field Error Analysis"
    }
    fig.suptitle(title_map.get(title, title), fontsize=16)
    plt.tight_layout(rect=[0, 0.03, 1, 0.95])

    # 保存图像
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    save_path = os.path.join(save_dir, f'error_analysis_{timestamp}.png')
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    print(f"误差分析图已保存到: {save_path}")
    plt.close()
    return save_path

def advanced_upsampling(data, target_shape, method='bspline'):
    """
    高质量上采样函数

    参数:
        data: 输入数据
        target_shape: 目标形状
        method: 插值方法 ('bspline', 'lanczos', 'cubic_improved')
    """
    try:
        if method == 'bspline':
            # B样条插值 - 最高质量
            from scipy.ndimage import zoom
            from scipy import ndimage

            # 使用B样条插值，order=5提供最平滑的结果
            zoom_factor_x = target_shape[0] / data.shape[0]
            zoom_factor_y = target_shape[1] / data.shape[1]

            # 先进行边界扩展，减少边界伪影
            padded_data = np.pad(data, ((2, 2), (2, 2)), mode='reflect')

            upsampled = zoom(
                padded_data.astype(np.float64),
                (zoom_factor_x, zoom_factor_y),
                order=5,  # B样条插值
                mode='mirror',
                prefilter=True
            )

            # 移除填充并调整到精确尺寸
            pad_x = int(2 * zoom_factor_x)
            pad_y = int(2 * zoom_factor_y)
            upsampled = upsampled[pad_x:-pad_x, pad_y:-pad_y]

            return precise_resize(upsampled, target_shape).astype(np.float32)

        elif method == 'lanczos':
            # Lanczos插值 - 高质量，锐利边缘
            try:
                from PIL import Image
                import numpy as np

                # 转换为PIL图像
                data_normalized = ((data - np.min(data)) / (np.max(data) - np.min(data) + 1e-10) * 255).astype(np.uint8)
                pil_image = Image.fromarray(data_normalized, mode='L')

                # 使用Lanczos重采样
                resized_pil = pil_image.resize(target_shape[::-1], Image.LANCZOS)

                # 转换回numpy并恢复原始范围
                resized_array = np.array(resized_pil).astype(np.float32) / 255.0
                data_min, data_max = np.min(data), np.max(data)
                resized_array = resized_array * (data_max - data_min) + data_min

                return resized_array

            except ImportError:
                print("PIL不可用，跳过Lanczos插值")
                return None

        elif method == 'cubic_improved':
            # 改进的三次插值
            from scipy.ndimage import zoom

            # 使用多步插值减少伪影
            current_data = data.astype(np.float64)
            current_shape = np.array(data.shape)
            target_shape_array = np.array(target_shape)

            # 如果缩放比例很大，分步进行
            total_zoom = target_shape_array / current_shape
            max_zoom = np.max(total_zoom)

            if max_zoom > 3:
                # 分两步进行上采样
                intermediate_zoom = np.sqrt(total_zoom)
                intermediate_shape = (current_shape * intermediate_zoom).astype(int)

                # 第一步
                current_data = zoom(
                    current_data,
                    intermediate_zoom,
                    order=3,
                    mode='reflect',
                    prefilter=True
                )

                # 第二步
                final_zoom = target_shape_array / intermediate_shape
                current_data = zoom(
                    current_data,
                    final_zoom,
                    order=3,
                    mode='reflect',
                    prefilter=True
                )
            else:
                # 一步完成
                current_data = zoom(
                    current_data,
                    total_zoom,
                    order=3,
                    mode='reflect',
                    prefilter=True
                )

            return precise_resize(current_data, target_shape).astype(np.float32)

    except Exception as e:
        print(f"高质量插值方法 {method} 失败: {str(e)}")
        return None

def precise_resize(data, target_shape):
    """精确调整数组尺寸"""
    if data.shape == target_shape:
        return data

    result = np.zeros(target_shape, dtype=data.dtype)
    min_h = min(data.shape[0], target_shape[0])
    min_w = min(data.shape[1], target_shape[1])

    # 居中放置
    start_h = (target_shape[0] - min_h) // 2
    start_w = (target_shape[1] - min_w) // 2

    result[start_h:start_h+min_h, start_w:start_w+min_w] = data[:min_h, :min_w]

    return result

def simple_upsampling(data, target_shape):
    """简单但稳定的上采样方法"""
    from scipy.ndimage import zoom

    zoom_factor_x = target_shape[0] / data.shape[0]
    zoom_factor_y = target_shape[1] / data.shape[1]

    upsampled = zoom(
        data.astype(np.float64),
        (zoom_factor_x, zoom_factor_y),
        order=1,  # 线性插值，最稳定
        mode='nearest'
    )

    return precise_resize(upsampled, target_shape).astype(np.float32)

def predict_with_original_resolution(model, input_field, downsample_factor=1, norm_params=None):
    """使用原始分辨率进行预测"""
    try:
        # 如果有降采样，先降采样输入
        if downsample_factor > 1:
            try:
                from scipy.ndimage import zoom
                zoom_factor = 1.0 / downsample_factor
                downsampled_input = zoom(input_field, zoom_factor, order=3)
            except Exception as e:
                print(f"降采样失败: {str(e)}，使用简单步长降采样")
                downsampled_input = input_field[::downsample_factor, ::downsample_factor]
        else:
            downsampled_input = input_field

        # 重塑为模型输入格式
        model_input = downsampled_input.reshape(1, downsampled_input.shape[0], downsampled_input.shape[1], 1)

        # 预测
        prediction = model.predict(model_input, verbose=0)

        # 重塑预测结果
        pred_shape = downsampled_input.shape
        prediction_reshaped = prediction.reshape(pred_shape)

        # 如果有降采样，需要上采样回原始分辨率
        if downsample_factor > 1:
            try:
                print(f"开始高质量上采样: {prediction_reshaped.shape} -> {input_field.shape}")

                # 方法1: 使用高质量的B样条插值
                prediction_upsampled = advanced_upsampling(
                    prediction_reshaped, input_field.shape, method='bspline'
                )

                if prediction_upsampled is not None:
                    print("✅ B样条插值上采样成功")
                else:
                    # 方法2: 使用Lanczos插值
                    print("尝试Lanczos插值...")
                    prediction_upsampled = advanced_upsampling(
                        prediction_reshaped, input_field.shape, method='lanczos'
                    )

                    if prediction_upsampled is not None:
                        print("✅ Lanczos插值上采样成功")
                    else:
                        # 方法3: 使用改进的三次插值
                        print("使用改进的三次插值...")
                        prediction_upsampled = advanced_upsampling(
                            prediction_reshaped, input_field.shape, method='cubic_improved'
                        )

                        if prediction_upsampled is None:
                            raise Exception("所有高质量插值方法都失败")

                print(f"上采样完成: {prediction_upsampled.shape}")

            except Exception as e:
                print(f"高质量上采样失败: {str(e)}，使用标准方法")
                # 使用改进的标准方法作为备选
                try:
                    from scipy.ndimage import zoom

                    # 使用更保守但稳定的插值设置
                    prediction_reshaped_float = prediction_reshaped.astype(np.float64)

                    # 计算精确的缩放因子
                    zoom_factor_x = input_field.shape[0] / prediction_reshaped.shape[0]
                    zoom_factor_y = input_field.shape[1] / prediction_reshaped.shape[1]

                    # 使用二次插值，平衡质量和稳定性
                    prediction_upsampled = zoom(
                        prediction_reshaped_float,
                        (zoom_factor_x, zoom_factor_y),
                        order=2,  # 二次插值
                        mode='mirror',  # 镜像边界，更自然
                        prefilter=True
                    )

                    # 精确尺寸调整
                    if prediction_upsampled.shape != input_field.shape:
                        prediction_upsampled = precise_resize(prediction_upsampled, input_field.shape)

                    prediction_upsampled = prediction_upsampled.astype(np.float32)
                    print("✅ 标准方法上采样完成")

                except Exception as e2:
                    print(f"标准方法也失败: {str(e2)}，使用最简单的方法")
                    # 最后的备选方案
                    prediction_upsampled = simple_upsampling(prediction_reshaped, input_field.shape)
                    print("✅ 简单方法上采样完成")
        else:
            prediction_upsampled = prediction_reshaped

        # 如果有归一化参数，进行反归一化
        if norm_params is not None:
            if 'mean' in norm_params and 'std' in norm_params:
                # Z-score反归一化
                prediction_upsampled = prediction_upsampled * norm_params['std'] + norm_params['mean']
            elif 'min' in norm_params and 'max' in norm_params:
                # Min-Max反归一化
                prediction_upsampled = prediction_upsampled * (norm_params['max'] - norm_params['min']) + norm_params['min']

        return prediction_upsampled.astype(np.float32)

    except Exception as e:
        print(f"预测过程中出错: {str(e)}")
        return np.zeros_like(input_field, dtype=np.float32)

def generate_multi_step_predictions(model, data_dir, field_type, start_step, end_step, step_interval,
                                   downsample_factor=1, norm_params=None, save_dir=None):
    """生成多时间步预测结果对比图"""
    try:
        print(f"开始生成{field_type}场多时间步预测...")

        # 根据场类型选择数据加载函数
        if field_type == 'phase':
            load_func = load_phase_data
            field_name = "Phase Field"
            field_name_cn = "相场"
        elif field_type == 'temp':
            load_func = load_temp_data
            field_name = "Temperature Field"
            field_name_cn = "温度场"
        else:  # solute
            load_func = load_solute_data
            field_name = "Concentration Field"
            field_name_cn = "溶质场"

        # 生成预测步数列表
        prediction_steps = list(range(start_step, end_step + 1, step_interval))

        # 创建保存目录
        if save_dir is None:
            save_dir = os.path.dirname(data_dir)
        multi_step_dir = os.path.join(save_dir, f"{field_name}_MultiStep_Predictions")
        os.makedirs(multi_step_dir, exist_ok=True)

        predictions = []
        ground_truths = []

        for step in prediction_steps:
            try:
                # 加载单个时间步数据
                data = load_func(data_dir, start_step=step, end_step=step, stride=1, max_samples=1)
                if len(data) == 0:
                    print(f"警告: 无法加载第{step}步数据")
                    continue

                # 进行预测
                prediction = predict_with_original_resolution(
                    model, data[0], downsample_factor=downsample_factor, norm_params=norm_params
                )

                predictions.append(prediction)
                ground_truths.append(data[0])

                # 保存单个预测结果
                single_save_path = visualize_prediction(
                    data[0], prediction, f"{field_name.lower()}",
                    data_dir, reshape_pred=False, save_dir=multi_step_dir
                )
                print(f"第{step}步预测完成，保存到: {single_save_path}")

            except Exception as e:
                print(f"处理第{step}步时出错: {str(e)}")
                continue

        # 生成综合对比图
        if len(predictions) > 0:
            try:
                fig, axes = plt.subplots(2, len(predictions), figsize=(4*len(predictions), 8))
                if len(predictions) == 1:
                    axes = axes.reshape(2, 1)

                for i, (gt, pred, step) in enumerate(zip(ground_truths, predictions, prediction_steps)):
                    vmin = min(np.min(gt), np.min(pred))
                    vmax = max(np.max(gt), np.max(pred))

                    # 真实场
                    im1 = axes[0, i].imshow(gt, cmap='jet', vmin=vmin, vmax=vmax)
                    axes[0, i].set_title(f'Ground Truth\nStep {step}')
                    axes[0, i].axis('off')

                    # 预测场
                    im2 = axes[1, i].imshow(pred, cmap='jet', vmin=vmin, vmax=vmax)
                    axes[1, i].set_title(f'Prediction\nStep {step}')
                    axes[1, i].axis('off')

                plt.tight_layout()
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                summary_path = os.path.join(multi_step_dir, f'{field_name}_multi_step_summary_{timestamp}.png')
                plt.savefig(summary_path, dpi=300, bbox_inches='tight')
                print(f"多步预测综合图已保存到: {summary_path}")
                plt.close()

            except Exception as e:
                print(f"生成综合对比图时出错: {str(e)}")

        print(f"{field_name}多时间步预测完成，共生成{len(predictions)}个预测结果")
        return len(predictions)

    except Exception as e:
        print(f"多步预测过程中出错: {str(e)}")
        return 0