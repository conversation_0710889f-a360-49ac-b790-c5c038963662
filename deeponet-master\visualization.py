import numpy as np
import matplotlib.pyplot as plt
import tensorflow as tf
from matplotlib.colors import Normalize
import os
from mpl_toolkits.axes_grid1 import make_axes_locatable
from ch_solver import CHSolver
from deeponet_ch import DeepONet

def generate_test_case(nx=64, ny=64, n_steps=200, save_interval=10):
    """生成一个新的测试案例"""
    print("生成新的测试案例...")
    
    # 创建求解器
    solver = CHSolver(nx=nx, ny=ny, dt=1e-4, epsilon=0.05)
    
    # 创建特定的初始条件
    x = np.linspace(0, solver.Lx, solver.nx)
    y = np.linspace(0, solver.Ly, solver.ny)
    X, Y = np.meshgrid(x, y, indexing='ij')
    
    # 使用两个高斯形状作为初始条件
    sigma = 0.1
    u0 = np.exp(-((X-0.3)**2 + (Y-0.3)**2)/(2*sigma**2))
    u0 += np.exp(-((X-0.7)**2 + (Y-0.7)**2)/(2*sigma**2))
    u0 = u0 - 0.5  # 确保平均值接近零
    u0 = np.clip(u0, -1, 1)
    
    # 求解方程
    print("求解CH方程...")
    solutions, times = solver.solve(u0, n_steps=n_steps, save_interval=save_interval)
    
    # 为DeepONet准备传感器输入
    m = min(64, nx)  # 传感器数量
    sensor_indices_x = np.linspace(0, nx-1, m, dtype=int)
    sensor_indices_y = np.linspace(0, ny-1, m, dtype=int)
    
    # 获取传感器值
    sensors_u0 = u0[np.ix_(sensor_indices_x, sensor_indices_y)].flatten()
    
    return solver, u0, solutions, times, sensors_u0

def predict_solution(model, sensors_u0, nx=64, ny=64, t=0.01):
    """使用DeepONet模型预测特定时间的整个网格解"""
    # 创建评估网格
    x = np.linspace(0, 1, nx)
    y = np.linspace(0, 1, ny)
    X, Y = np.meshgrid(x, y, indexing='ij')
    points = np.column_stack((X.flatten(), Y.flatten(), np.ones_like(X.flatten()) * t))
    
    # 准备模型输入
    X_u = np.tile(sensors_u0, (nx*ny, 1))
    
    # 预测
    predictions = model.predict(X_u, points)
    
    # 重塑为网格形状
    return predictions.reshape(nx, ny)

def plot_comparison(true_sol, pred_sol, time, filename, title=None):
    """绘制真实解和预测解的对比图"""
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    
    # 设置颜色范围
    vmin = min(np.min(true_sol), np.min(pred_sol))
    vmax = max(np.max(true_sol), np.max(pred_sol))
    norm = Normalize(vmin=vmin, vmax=vmax)
    
    # 绘制真实解
    im0 = axes[0].imshow(true_sol, cmap='coolwarm', norm=norm)
    axes[0].set_title(f'groud truth (t = {time:.4f})')
    axes[0].set_xlabel('x')
    axes[0].set_ylabel('y')
    divider = make_axes_locatable(axes[0])
    cax = divider.append_axes("right", size="5%", pad=0.1)
    plt.colorbar(im0, cax=cax)
    
    # 绘制预测解
    im1 = axes[1].imshow(pred_sol, cmap='coolwarm', norm=norm)
    axes[1].set_title(f'DeepONet predicate (t = {time:.4f})')
    axes[1].set_xlabel('x')
    axes[1].set_ylabel('y')
    divider = make_axes_locatable(axes[1])
    cax = divider.append_axes("right", size="5%", pad=0.1)
    plt.colorbar(im1, cax=cax)
    
    # 绘制误差
    error = np.abs(true_sol - pred_sol)
    im2 = axes[2].imshow(error, cmap='viridis')
    axes[2].set_title(f'loss (MAE = {np.mean(error):.4e})')
    axes[2].set_xlabel('x')
    axes[2].set_ylabel('y')
    divider = make_axes_locatable(axes[2])
    cax = divider.append_axes("right", size="5%", pad=0.1)
    plt.colorbar(im2, cax=cax)
    
    if title:
        fig.suptitle(title, fontsize=16)
    
    plt.tight_layout()
    plt.savefig(filename)
    plt.close()

def plot_1d_comparison(true_sol, pred_sol, time, pos=0.5, axis='x', filename='1d_comparison.png'):
    """绘制一个1D截面的真实解和预测解的对比图"""
    plt.figure(figsize=(10, 6))
    
    nx, ny = true_sol.shape
    
    if axis == 'x':
        # 沿x轴的截面
        i = int(pos * (ny - 1))
        x = np.linspace(0, 1, nx)
        plt.plot(x, true_sol[:, i], 'b-', linewidth=2, label='truth')
        plt.plot(x, pred_sol[:, i], 'r--', linewidth=2, label='DeepONet predicate')
        plt.xlabel('x')
    else:
        # 沿y轴的截面
        i = int(pos * (nx - 1))
        y = np.linspace(0, 1, ny)
        plt.plot(y, true_sol[i, :], 'b-', linewidth=2, label='truth')
        plt.plot(y, pred_sol[i, :], 'r--', linewidth=2, label='DeepONet predicate')
        plt.xlabel('y')
    
    plt.ylabel('u')
    plt.title(f'truth vs predicate (t = {time:.4f}, {axis} = {pos:.2f})')
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.legend()
    
    plt.tight_layout()
    plt.savefig(filename)
    plt.close()

def generate_error_table(model, true_solutions, times, sensors_u0, nx=64, ny=64):
    """生成不同时间点的误差表格"""
    results = []
    
    for i, t in enumerate(times):
        true_sol = true_solutions[i]
        pred_sol = predict_solution(model, sensors_u0, nx, ny, t)
        
        # 计算各种误差指标
        error = true_sol - pred_sol
        mae = np.mean(np.abs(error))
        rmse = np.sqrt(np.mean(error**2))
        max_error = np.max(np.abs(error))
        
        results.append({
            'time': t,
            'mae': mae,
            'rmse': rmse,
            'max_error': max_error
        })
    
    # 打印结果表格
    print("\n时间点 | MAE | RMSE | 最大误差")
    print("-" * 50)
    for r in results:
        print(f"{r['time']:.4f} | {r['mae']:.2e} | {r['rmse']:.2e} | {r['max_error']:.2e}")
    
    return results

def visualize_predictions(model_path, nx=64, ny=64, n_steps=100, save_interval=10):
    """可视化DeepONet模型对CH方程的预测结果"""
    # 创建输出目录
    os.makedirs('results', exist_ok=True)
    
    # 生成测试案例
    solver, u0, solutions, times, sensors_u0 = generate_test_case(
        nx=nx, ny=ny, n_steps=n_steps, save_interval=save_interval
    )
    
    # 加载训练好的模型
    print("加载模型:", model_path)
    model = DeepONet(branch_dim=len(sensors_u0), trunk_dim=3)
    model.model = tf.keras.models.load_model(model_path)
    
    # 对每个保存的时间步进行对比
    print("生成预测结果...")
    for i, t in enumerate(times):
        true_sol = solutions[i]
        
        # 预测整个网格的解
        pred_sol = predict_solution(model, sensors_u0, nx, ny, t)
        
        # 绘制2D对比图
        plot_comparison(
            true_sol, pred_sol, t,
            f'results/comparison_t{i:03d}.png',
            title=f'Cahn-Hilliard方程 - DeepONet预测 vs 真实解'
        )
        
        # 绘制1D截面对比图 (沿x轴中间)
        plot_1d_comparison(
            true_sol, pred_sol, t, pos=0.5, axis='x',
            filename=f'results/1d_comparison_x_t{i:03d}.png'
        )
        
        # 绘制1D截面对比图 (沿y轴中间)
        plot_1d_comparison(
            true_sol, pred_sol, t, pos=0.5, axis='y',
            filename=f'results/1d_comparison_y_t{i:03d}.png'
        )
    
    # 生成误差表格
    print("\n生成误差统计:")
    results = generate_error_table(model, solutions, times, sensors_u0, nx, ny)
    
    # 绘制误差随时间变化曲线
    plt.figure(figsize=(12, 6))
    times_array = np.array([r['time'] for r in results])
    mae_array = np.array([r['mae'] for r in results])
    rmse_array = np.array([r['rmse'] for r in results])
    max_error_array = np.array([r['max_error'] for r in results])
    
    plt.plot(times_array, mae_array, 'b-o', label='MAE')
    plt.plot(times_array, rmse_array, 'r-s', label='RMSE')
    plt.plot(times_array, max_error_array, 'g-^', label='max-loss')
    
    plt.xlabel('time')
    plt.ylabel('loss')
    plt.title('DeepONet-loss-time')
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.legend()
    plt.yscale('log')
    
    plt.tight_layout()
    plt.savefig('results/error_vs_time.png')
    plt.close()
    
    print("\n可视化完成！结果保存在'results/'目录中。")

if __name__ == "__main__":
    # 检查模型文件是否存在
    model_path = 'models/deeponet_best.h5'
    if not os.path.exists(model_path):
        print(f"模型文件 {model_path} 不存在，请先运行 deeponet_ch.py 训练模型")
        exit(1)
    
    # 可视化预测结果
    visualize_predictions(model_path, nx=64, ny=64, n_steps=100, save_interval=10) 