#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
溶质场多行对比可视化工具
基于温度场版本修改，适配溶质场数据特点
"""

import numpy as np
import matplotlib.pyplot as plt
import pandas as pd
import os
import argparse
from datetime import datetime

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False

def load_solute_data_single_step(data_dir, step):
    """加载单个时间步的溶质场数据"""
    file_path = os.path.join(data_dir, f"溶质场第{step}步.csv")
    if not os.path.exists(file_path):
        raise FileNotFoundError(f"文件不存在: {file_path}")
    
    data = pd.read_csv(file_path, header=None, encoding='utf-8').values.astype(np.float32)
    return data

def extract_line_data(data, line_type, line_index):
    """从2D数据中提取指定行或列的数据"""
    if line_type == 'row':
        if line_index >= data.shape[0]:
            raise ValueError(f"行索引 {line_index} 超出数据范围 [0, {data.shape[0]-1}]")
        return data[line_index, :]
    elif line_type == 'col':
        if line_index >= data.shape[1]:
            raise ValueError(f"列索引 {line_index} 超出数据范围 [0, {data.shape[1]-1}]")
        return data[:, line_index]
    else:
        raise ValueError("line_type 必须是 'row' 或 'col'")

def plot_multi_lines(data, line_indices, line_type='row', save_path=None, title_suffix=""):
    """绘制多条线的对比图"""
    plt.style.use('default')
    fig, ax = plt.subplots(figsize=(14, 8), facecolor='white')
    ax.set_facecolor('white')
    
    # 设置颜色
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
    
    all_lines_data = []
    
    for i, line_index in enumerate(line_indices):
        try:
            line_data = extract_line_data(data, line_type, line_index)
            x_coords = np.arange(len(line_data))
            
            color = colors[i % len(colors)]
            line_label = f'{line_type.title()} {line_index}'
            
            ax.plot(x_coords, line_data, color=color, linewidth=2.5, 
                   label=line_label, alpha=0.9)
            
            all_lines_data.append(line_data)
            
            print(f"✅ {line_type.title()} {line_index}: 溶质浓度范围[{line_data.min():.6f}, {line_data.max():.6f}]")
            
        except Exception as e:
            print(f"⚠️  跳过 {line_type} {line_index}: {str(e)}")
            continue
    
    if not all_lines_data:
        print("❌ 没有有效的数据可以绘制")
        return None
    
    # 设置图形属性
    axis_label = 'X' if line_type == 'row' else 'Y'
    
    ax.set_xlabel(f'{axis_label} Coordinate', fontsize=12, fontweight='bold')
    ax.set_ylabel('Solute Concentration', fontsize=12, fontweight='bold')
    ax.set_title(f'Solute Field Multi-Line Comparison {title_suffix}', 
                fontsize=14, fontweight='bold', pad=15)
    
    ax.legend(fontsize=10, frameon=True, fancybox=True, shadow=True, 
             loc='upper right', bbox_to_anchor=(0.98, 0.98))
    ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
    ax.set_axisbelow(True)
    
    # 设置坐标轴样式
    ax.spines['top'].set_visible(False)
    ax.spines['right'].set_visible(False)
    ax.spines['left'].set_linewidth(0.8)
    ax.spines['bottom'].set_linewidth(0.8)
    
    # 计算统计信息
    all_data = np.concatenate(all_lines_data)
    
    # 添加统计信息框
    stats_text = f'Lines: {len(all_lines_data)}\n' \
                f'Conc Range: [{all_data.min():.6f}, {all_data.max():.6f}]\n' \
                f'Mean Conc: {all_data.mean():.6f}\n' \
                f'Std Conc: {all_data.std():.6f}'
    
    ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
           verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5',
           facecolor='lightcyan', alpha=0.8, edgecolor='teal', linewidth=1))
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
        print(f"📊 图表已保存到: {save_path}")
    
    plt.show()
    return all_lines_data

def plot_multi_lines_gt_vs_pred(data_dir, time_step, pred_data, line_indices, 
                               line_type='row', save_path=None):
    """绘制Ground Truth vs Prediction多线对比图"""
    try:
        # 加载Ground Truth数据
        gt_data = load_solute_data_single_step(data_dir, time_step)
        
        plt.style.use('default')
        fig, ax = plt.subplots(figsize=(16, 10), facecolor='white')
        ax.set_facecolor('white')
        
        # 设置颜色
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f']
        
        all_gt_lines = []
        all_pred_lines = []
        
        for i, line_index in enumerate(line_indices):
            try:
                # 提取GT和Pred数据
                gt_line = extract_line_data(gt_data, line_type, line_index)
                pred_line = extract_line_data(pred_data, line_type, line_index)
                
                x_coords = np.arange(len(gt_line))
                color = colors[i % len(colors)]
                
                # 绘制GT和Pred
                ax.plot(x_coords, gt_line, color=color, linewidth=2.5, 
                       label=f'{line_type.title()} {line_index} GT', alpha=0.9, linestyle='-')
                ax.plot(x_coords, pred_line, color=color, linewidth=2.5, 
                       label=f'{line_type.title()} {line_index} Pred', alpha=0.9, linestyle='--')
                
                all_gt_lines.append(gt_line)
                all_pred_lines.append(pred_line)
                
                # 计算误差
                mae = np.mean(np.abs(gt_line - pred_line))
                print(f"✅ {line_type.title()} {line_index}: GT范围[{gt_line.min():.6f}, {gt_line.max():.6f}], "
                      f"Pred范围[{pred_line.min():.6f}, {pred_line.max():.6f}], MAE: {mae:.8f}")
                
            except Exception as e:
                print(f"⚠️  跳过 {line_type} {line_index}: {str(e)}")
                continue
        
        if not all_gt_lines:
            print("❌ 没有有效的数据可以绘制")
            return None
        
        # 设置图形属性
        axis_label = 'X' if line_type == 'row' else 'Y'
        
        ax.set_xlabel(f'{axis_label} Coordinate', fontsize=12, fontweight='bold')
        ax.set_ylabel('Solute Concentration', fontsize=12, fontweight='bold')
        ax.set_title(f'Solute Field GT vs Prediction - Step {time_step}', 
                    fontsize=14, fontweight='bold', pad=15)
        
        # 优化图例显示
        handles, labels = ax.get_legend_handles_labels()
        reordered_handles = []
        reordered_labels = []
        n_lines = len(all_gt_lines)
        for i in range(n_lines):
            gt_idx = i * 2
            pred_idx = i * 2 + 1
            if gt_idx < len(handles):
                reordered_handles.append(handles[gt_idx])
                reordered_labels.append(labels[gt_idx])
            if pred_idx < len(handles):
                reordered_handles.append(handles[pred_idx])
                reordered_labels.append(labels[pred_idx])
        
        ax.legend(reordered_handles, reordered_labels, fontsize=10, frameon=True, 
                 fancybox=True, shadow=True, loc='upper right', 
                 bbox_to_anchor=(0.98, 0.98), ncol=2)
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.set_axisbelow(True)
        
        # 设置坐标轴样式
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(0.8)
        ax.spines['bottom'].set_linewidth(0.8)
        
        # 计算统计信息
        all_gt_data = np.concatenate(all_gt_lines)
        all_pred_data = np.concatenate(all_pred_lines)
        
        # 计算误差统计
        errors = []
        for gt_line, pred_line in zip(all_gt_lines, all_pred_lines):
            error = np.mean(np.abs(gt_line - pred_line))
            errors.append(error)
        
        mean_error = np.mean(errors)
        max_error = np.max(errors)
        
        # 添加统计信息框
        stats_text = f'Lines: {len(all_gt_lines)}\n' \
                    f'Time Step: {time_step}\n' \
                    f'Mean MAE: {mean_error:.8f}\n' \
                    f'Max MAE: {max_error:.8f}\n' \
                    f'GT Range: [{all_gt_data.min():.6f}, {all_gt_data.max():.6f}]\n' \
                    f'Pred Range: [{all_pred_data.min():.6f}, {all_pred_data.max():.6f}]'
        
        ax.text(0.02, 0.98, stats_text, transform=ax.transAxes, fontsize=10,
               verticalalignment='top', bbox=dict(boxstyle='round,pad=0.5',
               facecolor='lightcyan', alpha=0.8, edgecolor='teal', linewidth=1))
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight', facecolor='white')
            print(f"📊 图表已保存到: {save_path}")
        
        plt.show()
        return all_gt_lines, all_pred_lines
        
    except Exception as e:
        print(f"❌ 绘制GT vs Prediction对比图时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def parse_line_indices(indices_str):
    """解析行/列索引字符串"""
    try:
        if ',' in indices_str:
            # 逗号分隔的索引列表
            indices = [int(x.strip()) for x in indices_str.split(',')]
        elif '-' in indices_str:
            # 范围格式，如 "100-500"
            start, end = map(int, indices_str.split('-'))
            indices = list(range(start, end + 1, max(1, (end - start) // 10)))  # 最多10条线
        else:
            # 单个索引
            indices = [int(indices_str)]
        return indices
    except ValueError:
        raise ValueError("索引格式错误，请使用逗号分隔的数字或范围格式（如：100,200,300 或 100-500）")

def interactive_solute_multi_line_console():
    """交互式溶质场多行对比控制台"""
    print("🎯 " + "="*60)
    print("    溶质场多行对比可视化工具")
    print("="*64)

    # 选择对比模式
    print("\n📊 选择对比模式:")
    print("  1. Prediction Only (仅预测结果)")
    print("  2. Ground Truth Only (仅真实数据)")
    print("  3. GT vs Prediction (真实数据 vs 预测结果对比)")

    while True:
        mode_choice = input("请选择模式 (1/2/3, 默认3): ").strip()
        if mode_choice == '1':
            comparison_mode = 'prediction_only'
            break
        elif mode_choice == '2':
            comparison_mode = 'groundtruth_only'
            break
        elif mode_choice == '3' or mode_choice == '':
            comparison_mode = 'gt_vs_pred'
            break
        else:
            print("❌ 请输入 1、2 或 3")

    # 获取数据目录和结果目录
    if comparison_mode in ['groundtruth_only', 'gt_vs_pred']:
        # 需要Ground Truth数据
        default_data_dir = 'E:/Freezedata/ConcentrationField_Data/'
        data_dir = input(f"📂 请输入溶质场数据目录路径 (默认: {default_data_dir}): ").strip()
        if not data_dir:
            data_dir = default_data_dir

        if not os.path.exists(data_dir):
            print(f"❌ 目录不存在: {data_dir}")
            return None

        # 获取时间步
        while True:
            time_step_input = input("⏰ 请输入要分析的时间步 (默认: 2000): ").strip()
            if not time_step_input:
                time_step = 2000
                break
            try:
                time_step = int(time_step_input)
                if time_step < 0:
                    print("❌ 时间步必须为非负整数")
                    continue
                break
            except ValueError:
                print("❌ 请输入有效的整数")
    else:
        data_dir = None
        time_step = None

    if comparison_mode in ['prediction_only', 'gt_vs_pred']:
        # 需要预测结果数据
        default_results_dir = './models/results'
        results_dir = input(f"📂 请输入结果目录路径 (默认: {default_results_dir}): ").strip()
        if not results_dir:
            results_dir = default_results_dir

        if not os.path.exists(results_dir):
            print(f"❌ 目录不存在: {results_dir}")
            return None
    else:
        results_dir = os.path.join(os.path.dirname(data_dir), 'results') if data_dir else None

    # 选择对比类型
    print("\n📏 选择对比类型:")
    print("  1. Row (行对比)")
    print("  2. Column (列对比)")

    while True:
        type_choice = input("请选择类型 (1/2, 默认1): ").strip()
        if type_choice == '2':
            line_type = 'col'
            break
        elif type_choice == '1' or type_choice == '':
            line_type = 'row'
            break
        else:
            print("❌ 请输入 1 或 2")

    # 获取索引列表
    while True:
        indices_input = input(f"📍 请输入要对比的{line_type}索引 (如: 300,400,500 或 300-500, 默认: 350,400,450): ").strip()
        if not indices_input:
            indices_input = "350,400,450"

        try:
            line_indices = parse_line_indices(indices_input)
            if len(line_indices) > 10:
                print("⚠️  索引数量过多，将只使用前10个")
                line_indices = line_indices[:10]
            break
        except ValueError as e:
            print(f"❌ {str(e)}")

    # 设置保存路径
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    if comparison_mode == 'groundtruth_only':
        results_dir = os.path.join(os.path.dirname(data_dir), 'results')
        os.makedirs(results_dir, exist_ok=True)
        save_path = os.path.join(results_dir,
                                f'solute_multi_{line_type}_gt_{time_step}_{timestamp}.png')
    elif comparison_mode == 'prediction_only':
        save_path = os.path.join(results_dir,
                                f'solute_multi_{line_type}_pred_{timestamp}.png')
    else:  # gt_vs_pred
        results_dir = os.path.join(os.path.dirname(data_dir), 'results')
        os.makedirs(results_dir, exist_ok=True)
        save_path = os.path.join(results_dir,
                                f'solute_multi_{line_type}_gt_vs_pred_{time_step}_{timestamp}.png')

    print("\n" + "="*64)
    print("📋 配置摘要:")
    print(f"   对比模式: {comparison_mode}")
    if comparison_mode in ['groundtruth_only', 'gt_vs_pred']:
        print(f"   数据目录: {data_dir}")
        print(f"   时间步: {time_step}")
    if comparison_mode in ['prediction_only', 'gt_vs_pred']:
        print(f"   结果目录: {results_dir}")
    print(f"   对比类型: {line_type}")
    print(f"   索引列表: {line_indices}")
    print(f"   保存路径: {save_path}")
    print("="*64)

    return {
        'comparison_mode': comparison_mode,
        'data_dir': data_dir,
        'time_step': time_step,
        'results_dir': results_dir,
        'line_type': line_type,
        'line_indices': line_indices,
        'save_path': save_path
    }

def main():
    parser = argparse.ArgumentParser(description='溶质场多行对比可视化工具')
    parser.add_argument('-i', '--interactive', action='store_true',
                       help='启用交互式模式')
    parser.add_argument('--comparison_mode', type=str, choices=['prediction_only', 'groundtruth_only', 'gt_vs_pred'], default='gt_vs_pred',
                       help='对比模式: prediction_only, groundtruth_only, 或 gt_vs_pred')
    parser.add_argument('--data_dir', type=str, default='E:/Freezedata/ConcentrationField_Data/',
                       help='溶质场数据目录路径')
    parser.add_argument('--time_step', type=int, default=2000,
                       help='时间步')
    parser.add_argument('--results_dir', type=str, default='./models/results',
                       help='结果目录路径')
    parser.add_argument('--line_type', type=str, choices=['row', 'col'], default='row',
                       help='对比类型: row 或 col')
    parser.add_argument('--line_indices', type=str, default='350,400,450',
                       help='要对比的行/列索引，支持逗号分隔或范围格式')
    parser.add_argument('--save_path', type=str, default='./models/results/1-temp/concentration_profile.png',
                       help='图片保存路径')

    args = parser.parse_args()

    # 交互式模式
    if args.interactive:
        config = interactive_solute_multi_line_console()
        if config is None:
            return

        args.comparison_mode = config['comparison_mode']
        args.data_dir = config['data_dir']
        args.time_step = config['time_step']
        args.results_dir = config['results_dir']
        args.line_type = config['line_type']
        args.line_indices = config['line_indices']
        args.save_path = config['save_path']
    else:
        # 解析索引
        try:
            args.line_indices = parse_line_indices(args.line_indices)
        except ValueError as e:
            print(f"❌ 索引解析错误: {str(e)}")
            return

    # 设置保存路径
    if args.save_path is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        if args.comparison_mode == 'groundtruth_only':
            results_dir = os.path.join(os.path.dirname(args.data_dir), 'results')
            os.makedirs(results_dir, exist_ok=True)
            args.save_path = os.path.join(results_dir,
                                        f'solute_multi_{args.line_type}_gt_{args.time_step}_{timestamp}.png')
        elif args.comparison_mode == 'prediction_only':
            args.save_path = os.path.join(args.results_dir,
                                        f'solute_multi_{args.line_type}_pred_{timestamp}.png')
        else:  # gt_vs_pred
            results_dir = os.path.join(os.path.dirname(args.data_dir), 'results')
            os.makedirs(results_dir, exist_ok=True)
            args.save_path = os.path.join(results_dir,
                                        f'solute_multi_{args.line_type}_gt_vs_pred_{args.time_step}_{timestamp}.png')

    # 根据对比模式选择不同的处理流程
    if args.comparison_mode == 'groundtruth_only':
        # 仅绘制Ground Truth
        try:
            gt_data = load_solute_data_single_step(args.data_dir, args.time_step)
            all_lines_data = plot_multi_lines(gt_data, args.line_indices,
                                            args.line_type, args.save_path,
                                            title_suffix=f"(Ground Truth - Step {args.time_step})")

            if all_lines_data:
                print(f"\n📊 成功绘制 {len(all_lines_data)} 条线的Ground Truth对比图")

        except Exception as e:
            print(f"❌ 绘制Ground Truth对比图时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    elif args.comparison_mode == 'prediction_only':
        # 仅绘制预测结果
        solute_pred_path = os.path.join(args.results_dir, 'solute_prediction.npy')

        if not os.path.exists(solute_pred_path):
            print(f"❌ 未找到预测结果文件: {solute_pred_path}")
            print("请先运行溶质场预测生成结果文件")
            return

        # 加载预测结果
        print(f"📂 加载预测结果: {solute_pred_path}")
        pred_data = np.load(solute_pred_path)
        print(f"预测数据形状: {pred_data.shape}")

        # 绘制多线对比图
        try:
            all_lines_data = plot_multi_lines(pred_data, args.line_indices,
                                            args.line_type, args.save_path,
                                            title_suffix="(Prediction)")

            print(f"\n📊 成功绘制 {len(all_lines_data)} 条线的预测对比图")

        except Exception as e:
            print(f"❌ 绘制预测对比图时出错: {str(e)}")
            import traceback
            traceback.print_exc()

    else:  # gt_vs_pred
        # GT vs Prediction对比
        solute_pred_path = os.path.join(args.results_dir, 'solute_prediction.npy')

        if not os.path.exists(solute_pred_path):
            print(f"❌ 未找到预测结果文件: {solute_pred_path}")
            print("请先运行溶质场预测生成结果文件")
            return

        # 加载预测结果
        print(f"📂 加载预测结果: {solute_pred_path}")
        pred_data = np.load(solute_pred_path)
        print(f"预测数据形状: {pred_data.shape}")

        # 绘制GT vs Prediction对比图
        try:
            result = plot_multi_lines_gt_vs_pred(
                args.data_dir, args.time_step, pred_data, args.line_indices,
                args.line_type, args.save_path
            )

            if result:
                all_gt_lines, all_pred_lines = result
                print(f"\n📊 成功绘制 {len(all_gt_lines)} 条线的GT vs Prediction对比图")

        except Exception as e:
            print(f"❌ 绘制GT vs Prediction对比图时出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    main()
