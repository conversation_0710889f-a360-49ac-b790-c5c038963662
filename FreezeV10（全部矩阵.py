# -*- coding: utf-8 -*-
"""
Created on Sat Oct 30 11:06:44 2021
相场法计算
11.18更新，完成溶质场和相场
12.25 V7版，利用效率极低的for循环实现了温度场的计算，新增csv文件保存功能
4.29日 V9B1版，相场、溶质场、温度场已经矩阵；扰动矩阵化。
@author: 
"""

# import CoolProp.CoolProp as CP
# from CoolProp.Plots import PropertyPlot
import cupy as cp
import numpy as np
import matplotlib.pyplot as plt
import warnings,os,time,random
from numpy import *
import gc

def mtp(mat1,mat2):return cp.nan_to_num(cp.multiply(mat1,mat2))#矩阵元素乘
# def dvd(mat1,mat2):return np.nan_to_num(np.divide(mat1,mat2))#矩阵元素相除
def dvd(mat1,mat2):return cp.nan_to_num(cp.divide(mat1,mat2))#矩阵元素相除容易出错，这里把被除的矩阵加1e-20
#矩阵一阶和二阶倒
def delta_x(mat):return cp.nan_to_num((mat[2:,1:-1]-mat[0:-2,1:-1]))/2/dx
def delta_y(mat):return cp.nan_to_num((mat[1:-1,2:]-mat[1:-1,0:-2]))/2/dy
def delta_xx(mat):return cp.nan_to_num((mat[2:,1:-1]+mat[0:-2,1:-1]-2*mat[1:-1,1:-1]))/dx/dx
def delta_yy(mat):return cp.nan_to_num((mat[1:-1,2:]+mat[1:-1,0:-2]-2*mat[1:-1,1:-1]))/dy/dy
def delta_xy(mat):return cp.nan_to_num((mat[2:,2:]+mat[0:-2,0:-2]-mat[2:,0:-2]-mat[0:-2,2:]))/4/dx/dy
def full_matrix(mat):mat[:,[0,-1]]=mat[:,[1,-2]];mat[[0,-1],:]=mat[[1,-2],:];return mat#补全四周空隙
def Phase_solid(phi):return cp.nan_to_num(cp.array(phi>0.999))*1.0#判断phase为固态的区域，返回0,1的结果
def Phase_liquid(phi):return cp.nan_to_num(cp.array(phi<0.001))*1.0#判断phase为液态的区域，返回0,1的结果
def Phase_solid_liquid(phi):return mtp(cp.nan_to_num(cp.array(phi>0.001))*1,cp.nan_to_num(cp.array(phi<0.999))*1)#判断phase为固液两相的区域，返回0,1的结果

def Phase_pdsolid(phi):return cp.nan_to_num(cp.array(phi>0.9999))*1.0#判断phase为固态的区域，返回0,1的结果
def Phase_pdliquid(phi):return cp.nan_to_num(cp.array(phi<0.0001))*1.0#判断phase为液态的区域，返回0,1的结果
def Phase_pdsolid_liquid(phi):return mtp(cp.nan_to_num(cp.array(phi>0.0001))*1.0,cp.nan_to_num(cp.array(phi<0.9999))*1)

def Phase_ddsolid(phi):return cp.nan_to_num(cp.array(phi<0.9))*1#判断phase为固态的区域，返回0,1的结果

def fxn():warnings.warn("deprecated", DeprecationWarning)#禁止报警

def save_data(mat, title='未命名_'+time.strftime("%m%d_%H%MS", time.localtime()), fmts='%.04f', field_type=None):
    # 如果输入是CuPy数组，转换为NumPy数组
    if isinstance(mat, cp.ndarray):
        mat = cp.asnumpy(mat)
        
    # 根据场的类型选择保存路径
    if field_type:
        if os.name == 'nt':
            base_dir = os.getcwd()
        elif os.name == 'posix':
            base_dir = '~\\文档\\Freeze'
            
        # 选择对应的数据文件夹
        if field_type == '相场':
            data_dir = os.path.join(base_dir, 'PhaseField_Data')
        elif field_type == '温度场':
            data_dir = os.path.join(base_dir, 'TemperatureField_Data')
        elif field_type == '浓度场':
            data_dir = os.path.join(base_dir, 'ConcentrationField_Data')
            
        # 创建保存目录
        if not os.path.exists(data_dir):
            os.makedirs(data_dir)
            
        # 保存CSV文件
        fileName = os.path.join(data_dir, title + '.csv')
        np.savetxt(fileName, mat, delimiter=',', fmt=fmts)

def fig_cont(mat, title='no', field_type=None):
    # 如果输入是CuPy数组，转换为NumPy数组
    if isinstance(mat, cp.ndarray):
        mat = cp.asnumpy(mat)
    
    # 使用适中的图像质量设置
    plt.figure(figsize=(10, 8), dpi=150)
    plt.contourf(mat, alpha=0.75, cmap=plt.cm.jet, levels=50)
    if title != 'no':
        plt.title(title, fontproperties='SimHei', fontsize=14)
    plt.colorbar()
    
    # 根据场的类型选择保存路径
    if field_type:
        if os.name == 'nt':
            base_dir = os.getcwd()
        elif os.name == 'posix':
            base_dir = '~\\文档\\Freeze'
            
        # 选择对应的图像文件夹
        if field_type == '相场':
            save_dir = os.path.join(base_dir, 'PhaseField_Images')
        elif field_type == '温度场':
            save_dir = os.path.join(base_dir, 'TemperatureField_Images')
        elif field_type == '浓度场':
            save_dir = os.path.join(base_dir, 'ConcentrationField_Images')
        
        # 创建保存目录
        if not os.path.exists(save_dir):
            os.makedirs(save_dir)
        
        # 保存图像，只使用支持的参数
        plt.savefig(os.path.join(save_dir, title + '.png'), 
                   dpi=150,
                   bbox_inches='tight')
    
    # 立即清理内存
    plt.close('all')

def initial_com_phi():#初始化加增加颗粒
    phi0=cp.full((m+2,n+2),0.0)
    phi1=cp.full((m+2,n+2),0.0)
    com0=cp.full((m+2,n+2),co)
    com1=cp.full((m+2,n+2),co)
    acttmp0=cp.full((m+2,n+2),tmp)
    acttmp1=cp.full((m+2,n+2),tmp)
    #给初始核
    phi0[1:5,:]=1.0
    phi1[1:5,:]=1.0
    com0[1:5,:]=co*ke
    com1[1:5,:]=co*ke
    #补全四周空隙
    phi0=full_matrix(phi0)
    com0=full_matrix(com0)
    acttmp0=full_matrix(acttmp0)
    return phi0,phi1,com0,com1,acttmp0
def mobility():#相场迁移率M 
    zeta=0.0;
    fcc=cp.nan_to_num(Rvmt*(cle-cse)*(cle-cse));
    w=3.3*sigma/dx;
    for p1 in np.arange(0.001,0.998,0.001): #循环运算998次
        p2=p1+0.001;
        pp1=p1*p1*p1*(10.0-15.0*p1+6.0*p1*p1);
        pp2=p2*p2*p2*(10.0-15.0*p2+6.0*p2*p2);
        fun1=pp1*(1.0-pp1)/((1.0-pp1)*cle*(1-cle)+pp1*cse*(1-cse))/(p1*(1.0-p1));
        fun2=pp2*(1.0-pp2)/((1.0-pp2)*cle*(1-cle)+pp1*cse*(1-cse))/(p2*(1.0-p2));
        zeta=zeta+(fun1+fun2)*0.001/2.0;
    xm1=1.0/(ep*ep/sigma*ep/(dl*(2.0*w)**0.5)*zeta*fcc)
    return xm1
def progress_bar(i,total):#整个进度条
	if i+1 == total:
		percent = 100.0
		print('当前核算进度 : %s [%d/%d]'%(str(percent)+'%',i+1,total),end='\n')
	else:
		percent = round(1.0 * i / total * 100,2)
		print('当前核算进度 : %s [%d/%d]'%(str(percent)+'%',i+1,total),end='\r')
'''
写到这里
'''
    
def phi_noise(phi):#给一个随机扰动
    countnoise=cp.full((m+2,n+2),0.0)
    comtot=cp.full((m+2,n+2),0.0)
    comnoise=cp.full((m+2,n+2),0.03)
    conditions=mtp(cp.array(phi>0.01)*1.0,cp.array(phi<0.5)*1.0)
    p=mtp(phi,conditions)
    cnoise=32*mtp(conditions,mtp(mtp(mtp(p,p),mtp((1-p),(1-p))),(0.5-cp.random.rand(m+2,n+2)))*comnoise)
    phi+=mtp(p,cnoise)
    comtot=cp.sum(cp.sum(mtp(phi[1:-1,1:-1],cnoise[1:-1,1:-1])))
    countnoise=cp.sum(cp.sum(conditions))+(cp.sum(cp.sum(conditions))==0)*1e-20
    phi=phi-comtot/countnoise*conditions
    phi=full_matrix(phi)
    return phi


 
def pc_cal(phi,com):
    cl=cp.full((m+2,n+2),0.0)
    cs=cp.full((m+2,n+2),0.0)
    fcl=cp.full((m+2,n+2),0.0)
    fcc=cp.full((m+2,n+2),0.0)
    fcs=cp.full((m+2,n+2),0.0)
    
    ep2=ep*ep
    tmpe=acttmp0+0.0
    Rvmt=R*tmpe/vm
    cle=dvd((Tma-tmpe)+c1*Hd*Hd,xme)
    cse=mtp(cle,ke)
    a=mtp(dvd(cle,cse),dvd((1-cse),(1-cle)))
    pp=mtp(mtp(mtp(phi,phi),phi),(10-15*phi+6*mtp(phi,phi)))
    aa=mtp(pp,1-a)
    bb=pp+mtp(1-pp,a)+mtp(com,1-a)
    css=dvd((bb-(mtp(bb,bb)-4*mtp(aa,com))**0.5),2*aa)
    cll=dvd((com-mtp(pp,css)),1-pp)
    
    phi_s=Phase_solid(phi)
    phi_l=Phase_liquid(phi)
    phi_2=Phase_solid_liquid(phi)
    
    cl=mtp(phi_l,com)+mtp(phi_s,dvd(com,1+mtp((a-1),com)))+mtp(phi_2,cll)
    cs=mtp(phi_s,com)+mtp(phi_l,dvd(com,a+mtp((1-a),com)))+mtp(phi_2,css)
    
    fcl=mtp(Rvmt,cp.log(dvd(cl,(1.0-cl))))
    fcs=mtp(Rvmt,cp.log(dvd(cs,(1.0-cs))))
    fcc=dvd(Rvmt,mtp(com,1-com))
    
    pd=phi+0
    pd[1:-1,1:-1]=(phi[1:-1,2:]+phi[1:-1,0:-2]+phi[2:,1:-1]+phi[0:-2,1:-1])/4
    
    phi_pds=Phase_pdsolid(pd)
    phi_pdl=Phase_pdliquid(pd)
    phi_pd2=Phase_pdsolid_liquid(pd)
    
    dphi=0
    dc_l=dl*(delta_xx(com)+delta_yy(com))
    dc_s=ds*(delta_xx(com)+delta_yy(com))
    
    pg=30*mtp(mtp(phi,phi),mtp(1-phi,1-phi))
    gd=2*mtp(phi,mtp(1-phi,1-2*phi))
    gg=mtp(mtp(Rvmt,pg),(cp.log(mtp(dvd(1-cse,1-cle),dvd(1-cl,1-cs)))))
    fp=gg-w*gd
    phix=delta_x(phi)
    phiy=delta_y(phi)
    phixx=delta_xx(phi)
    phiyy=delta_yy(phi)
    phixy=delta_xy(phi)
    th=cp.arctan(dvd(phiy,(phix+1.0e-20)))
    eta=1.0+v*cp.cos(yk*th)
    deta=v*yk*cp.sin(yk*th)
    
    e1=ep2*mtp(mtp(eta,eta),(phixx+phiyy))
    e2=mtp(mtp(mtp(ep2,eta),mtp((-deta),(cp.sin(2.0*th)))),((phiyy-phixx)+2.0*mtp(cp.cos(2.0*th),phixy)))
    e3=0.5*ep2
    e4=mtp(deta,deta)+eta*(-v*yk*yk*cp.cos(yk*th))
    e5=2.0*mtp(cp.sin(2.0*th),phixy)-phixx-phiyy-mtp(cp.cos(2.0*th),(phiyy-phixx))
    dphi=(mtp(phi_pd2[1:-1,1:-1],xm1*(e1+e2-e3*mtp(e4,e5)+fp[1:-1,1:-1])))

    phi_dds=Phase_ddsolid(phi)
    d1=phi_dds[1:-1,1:-1]*dl+(1-phi_dds[1:-1,1:-1])*ds
    d2=phi_dds[:-2,1:-1]*dl+(1-phi_dds[:-2,1:-1])*ds
    d3=phi_dds[2:,1:-1]*dl+(1-phi_dds[2:,1:-1])*ds
    d4=phi_dds[1:-1,:-2]*dl+(1-phi_dds[1:-1,:-2])*ds
    d5=phi_dds[1:-1,2:]*dl+(1-phi_dds[1:-1,2:])*ds
    
    fccw=2.0*dvd(dvd(mtp(d1,d2),mtp(fcc[:-2,1:-1],fcc[1:-1,1:-1])),dvd(d1,fcc[1:-1,1:-1])+dvd(d2,fcc[:-2,1:-1]))
    fcce=2.0*dvd(dvd(mtp(d1,d3),mtp(fcc[2:,1:-1],fcc[1:-1,1:-1])),dvd(d1,fcc[1:-1,1:-1])+dvd(d3,fcc[2:,1:-1]))
    fccn=2.0*dvd(dvd(mtp(d1,d4),mtp(fcc[1:-1,:-2],fcc[1:-1,1:-1])),dvd(d1,fcc[1:-1,1:-1])+dvd(d4,fcc[1:-1,:-2]))
    fccs=2.0*dvd(dvd(mtp(d1,d5),mtp(fcc[1:-1,2:],fcc[1:-1,1:-1])),dvd(d1,fcc[1:-1,1:-1])+dvd(d5,fcc[1:-1,2:]))

    xj1=mtp(mtp(pp[1:-1,1:-1],-fcs[1:-1,1:-1]+fcs[:-2,1:-1])+mtp(1-pp[1:-1,1:-1],-fcl[1:-1,1:-1]+fcl[:-2,1:-1]),fccw)/dx
    xj2=mtp(mtp(pp[1:-1,1:-1],-fcs[1:-1,1:-1]+fcs[2:,1:-1])+mtp(1-pp[1:-1,1:-1],-fcl[1:-1,1:-1]+fcl[2:,1:-1]),fcce)/dx
    xj3=mtp(mtp(pp[1:-1,1:-1],-fcs[1:-1,1:-1]+fcs[1:-1,:-2])+mtp(1-pp[1:-1,1:-1],-fcl[1:-1,1:-1]+fcl[1:-1,:-2]),fccn)/dy
    xj4=mtp(mtp(pp[1:-1,1:-1],-fcs[1:-1,1:-1]+fcs[1:-1,2:])+mtp(1-pp[1:-1,1:-1],-fcl[1:-1,1:-1]+fcl[1:-1,2:]),fccs)/dy

    dc_2=(xj1+xj2)/dx+(xj3+xj4)/dy    
    dc=mtp(phi_pds[1:-1,1:-1],dc_s)+mtp(phi_pdl[1:-1,1:-1],dc_l)+(mtp(phi_pd2[1:-1,1:-1],dc_2))
    
    phi1=cp.full((m+2,n+2),0.0)
    com1=cp.full((m+2,n+2),0.0)
    
    phi1[1:-1,1:-1]=phi[1:-1,1:-1]+dphi*dt
    com1[1:-1,1:-1]=com[1:-1,1:-1]+dc*dt
    phi1=full_matrix(phi1)
    com1=full_matrix(com1)
    
    return phi1,com1  


def cal_tmp(phi0,phi1,com0,com1,acttmp0):#计算温度场（ADI） 第二边界条件
    dxt=dt/2
    RoCp=ro*Cp*dx*dy/(K*dxt)
     
    q=-Cp*50*1e5*m*dx/2
    qx=cp.full((m+2,n+2),0.0)
    qx[[0,1],:]+=q*dx/K
    qx=full_matrix(qx)
    ###########################
    ####******x隐y显*****##### 
    S=30*mtp(mtp(mtp(phi1,phi1),mtp(1-phi1,1-phi1))*ro*L,(phi1-phi0)/dxt/2)
    Sxy=cp.abs(S*dx*dy/K)
    
    tmp_a=cp.full((m+2,n+2),-1.0)
    tmp_a[[0,1],:]=0.
    tmp_b=cp.full((m+2,n+2),RoCp+2)
    tmp_b[[0,1,-2,-1],:]=RoCp+1
    tmp_c=cp.full((m+2,n+2),-1.0)
    tmp_c[-2,:]=0.0
    tmp_d=cp.full((m+2,n+2),0.0)
    tmp_d[1:-1,1:-1]=acttmp0[1:-1,0:-2]+(RoCp-2)*acttmp0[1:-1,1:-1]+acttmp0[1:-1,2:]+Sxy[1:-1,1:-1]+qx[1:-1,1:-1]
    tmp_d[1:-1,1]=(RoCp-1)*acttmp0[1:-1,1]+acttmp0[1:-1,2]+Sxy[1:-1,1]+qx[1:-1,1]
    tmp_d[1:-1,-2]=acttmp0[1:-1,-3]+(RoCp-1)*acttmp0[1:-1,-2]+Sxy[1:-1,-2]+qx[1:-1,-2]
    
    for mat in [tmp_a,tmp_b,tmp_c,tmp_d]:
        mat=full_matrix(mat)    
    
    bet=tmp_b+0.0
    tmp_x=dvd(tmp_d,bet)
    
    for i in range(2,n+1):
        bet[i,:]=tmp_b[i,:]-tmp_a[i,:]*tmp_c[i-1,:]/bet[i-1,:]
        tmp_x[i,:]=(tmp_d[i,:]-tmp_a[i,:]*tmp_x[i-1,:])/bet[i,:]
    for i in range(m-1,0,-1):
        tmp_x[i,:]=tmp_x[i,:]-tmp_c[i,:]/bet[i,:]*tmp_x[i+1,:]
    acttmp0_1=full_matrix(tmp_x)  
    
    tmp_a=cp.full((m+2,n+2),-1.0)
    tmp_a[:,[0,1]]=0.0
    tmp_b=cp.full((m+2,n+2),RoCp+2)
    tmp_b[:,[0,1,-2,-1]]=RoCp+1
    tmp_c=cp.full((m+2,n+2),-1.0)
    tmp_c[:,-2]=0.0
    tmp_d=cp.full((m+2,n+2),0.0)
    tmp_d[1:-1,1:-1]=acttmp0_1[0:-2,1:-1]+(RoCp-2)*acttmp0_1[1:-1,1:-1]+acttmp0_1[2:,1:-1]+Sxy[1:-1,1:-1]+qx[1:-1,1:-1]
    tmp_d[1,1:-1]=(RoCp-1)*acttmp0_1[1,1:-1]+acttmp0_1[2,1:-1]+Sxy[1,1:-1]+qx[1,1:-1]
    tmp_d[-2,1:-1]=acttmp0_1[-3,1:-1]+(RoCp-1)*acttmp0_1[-2,1:-1]+Sxy[-2,1:-1]+qx[-2,1:-1]
    
    gam=cp.full((m+2,n+2),0.0)
    bet=cp.full((m+2,n+2),0.0)
    
    bet[:,1]=tmp_b[:,1]
    tmp_x[:,1]=tmp_d[:,1]/bet[:,1]
    for k in range(2,m+1):
        gam[:,k]=tmp_c[:,k-1]/bet[:,k-1]
        bet[:,k]=tmp_b[:,k]-tmp_a[:,k]*gam[:,k]
        tmp_x[:,k]=(tmp_d[:,k]-tmp_a[:,k]*tmp_x[:,k-1])/bet[:,k]
    for k in range(m-1,0,-1):
        tmp_x[:,k]=tmp_x[:,k]-gam[:,k+1]*tmp_x[:,k+1]
    acttmp1=full_matrix(tmp_x)   
    return acttmp1
    

if __name__ == "__main__":
    time_init=time.time()
    m=n=800#网格大小
    time_total=5000#总时间步数
    
    # 添加保存间隔参数
    save_data_interval = 1     # 每隔多少步保存一次数据
    save_image_interval = 50  # 每隔多少步保存一次图像
    
    dx=dy=1e-7     #空间步长m
    sigma=0.0758   #界面能 J/m^2
    v=0.02         #各向异性强度系数
    yk=6.0         #各向异性强度模数
    co=0.03        #溶质浓度
    dl=5.68e-10    #液相溶质扩散系数 m^2/s
    ds=2.56e-11    #固相溶质扩散系数 m^2/s
    ke=0.075       #平衡常数
    xme=90         #负的液相线斜率
    Tma=273.15     #水熔点温度
    R=8.314
    vm=18e-6       #摩尔体积 m^3/mol
    tmp=258.15     #液相线温度或实际温度 K
    
    #温度场求解参数
    K=0.55         #热导率 J/(m*K*s)
    Cp=4168        #比热容 单位J/(kg*K)
    L=3.55e5       #潜热 单位J/kg
    ro=1e3         #密度 kg/m^3
    c1=2.03e-4
    Hd=0
    gf=3.35e8
    vc=3e-29
    cj=1
    l=0
    lsave=0
    
    Q=9            #D2Q9模型
    U=0.0000057    #入口速度
    W=cp.array([4.0/9,1.0/9,1.0/9,1.0/9,1.0/9,1.0/36,1.0/36,1.0/36,1.0/36])
    e=cp.array([[0,0],[1,0],[0,1],[-1,0],[0,-1],[1,1],[-1,1],[-1,-1],[1,-1]])
    gx=cp.full((m+1,n),0.0)
    gy=cp.full((m+1,n),0.0)
    cl=cp.full((m+2,n+2),0.0)
    
    rho=cp.full((m,n),1.0)
    tau=0.8
    w=3.3*sigma/dx
    u=cp.full((m+2,n+2,2),0.0)
    up=200*u
    
    phi0,phi1,com0,com1,acttmp0=initial_com_phi()
    
    cle=(Tma-tmp)/xme
    cse=cle*ke
    Rvmt=R*tmp/vm 
    dt=dx*dx/(5.0*dl)
    tmp_flag=1
    triangle=15
    ep=(12.0/2.2*dx*sigma)**0.5
    stepnum=10
    modsave=20
    num=modsave/stepnum
    xm1=mobility()
    
    with warnings.catch_warnings():
        warnings.simplefilter("ignore")
        fxn()
    
    print("已设置了所有的计算条件。\n")
    print("相场溶质场数据输出步长：%d 微秒\n"%(modsave*(dt)/(1e-6)))
    print("生长速度、半径、Pc数输出步长：：%d 微秒\n"%(num*(dt)/(1e-6)))
    print('开始计算 请稍后\n')
    
    random.seed(0)
    cp.random.seed(0)

    for loop_number in range(time_total):
        # 将GPU数据转移到CPU进行保存
        if loop_number % save_data_interval == 0:
            phi0_cpu = cp.asnumpy(phi0[1:-1,1:-1])
            com0_cpu = cp.asnumpy(com0[1:-1,1:-1])
            acttmp0_cpu = cp.asnumpy(acttmp0[1:-1,1:-1])
            
            try:
                # 保存数据
                save_data(phi0_cpu, u'相场第%d步'%loop_number, '%.04f', '相场')
                save_data(com0_cpu, u'溶质场第%d步'%loop_number, '%.04f', '浓度场')
                save_data(acttmp0_cpu, u'温度场第%d步'%loop_number, '%.04f', '温度场')
                
                # 保存图像
                if loop_number % save_image_interval == 0:
                    fig_cont(phi0_cpu, u'相场第%d步'%loop_number, '相场')
                    fig_cont(com0_cpu, u'溶质场第%d步'%loop_number, '浓度场')
                    fig_cont(acttmp0_cpu, u'温度场第%d步'%loop_number, '温度场')
                
                # 每次保存后进行垃圾回收
                gc.collect()
                cp.get_default_memory_pool().free_all_blocks()
                cp.get_default_pinned_memory_pool().free_all_blocks()
                
            except Exception as e:
                print(f"\n保存数据时出错: {str(e)}")
                continue
        
        print(f'\r当前计算进度: {loop_number}/{time_total}', end='')
             
        phi1,com1=pc_cal(phi0,com0)
        acttmp1=cal_tmp(phi0,phi1,com0,com1,acttmp0)
        phi0,com0,acttmp0=phi1,com1,acttmp1
        phi0=phi_noise(phi0)

    print('\n运行时间为%.2f秒'%(time.time()-time_init))    









