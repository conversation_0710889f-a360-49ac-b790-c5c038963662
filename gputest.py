import numpy as np
import tensorflow as tf
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
import os
import pandas as pd
import argparse  # 添加argparse库


# GPU配置和诊断
def setup_gpu():
    print("TensorFlow版本:", tf.__version__)

    # 检查CUDA是否可用
    print("\nCUDA配置:")
    print("CUDA是否可用:", tf.test.is_built_with_cuda())

    # 检测可用的GPU
    gpus = tf.config.list_physical_devices('GPU')
    if gpus:
        try:
            # 设置GPU显存按需分配
            for gpu in gpus:
                tf.config.experimental.set_memory_growth(gpu, True)
            print("\nGPU配置:")
            print(f"- 检测到 {len(gpus)} 个GPU设备")
            print("- GPU显存已设置为按需分配")

            # 设置GPU为默认设备
            tf.config.set_visible_devices(gpus[0], 'GPU')
            logical_gpus = tf.config.list_logical_devices('GPU')
            print(f"- 可用的逻辑GPU数量: {len(logical_gpus)}")

            # 测试GPU计算
            print("\n执行GPU测试计算...")
            with tf.device('/GPU:0'):
                a = tf.random.normal([1000, 1000])
                b = tf.random.normal([1000, 1000])
                c = tf.matmul(a, b)
            print("GPU测试完成：矩阵乘法运算成功")
            return True

        except RuntimeError as e:
            print("\nGPU配置错误:", str(e))
    else:
        print("\n未检测到GPU，请确保：")
        print("1. 已安装NVIDIA GPU硬件")
        print("2. 已安装最新的NVIDIA驱动程序")
        print("3. 已安装CUDA Toolkit（建议11.8版本）")
        print("4. 已安装cuDNN（与CUDA版本匹配）")
        print("5. 已安装tensorflow-gpu或正确版本的tensorflow")
        print("\n您可以访问以下链接获取安装指南：")
        print("- NVIDIA驱动：https://www.nvidia.com/Download/index.aspx")
        print("- CUDA Toolkit：https://developer.nvidia.com/cuda-toolkit")
        print("- cuDNN：https://developer.nvidia.com/cudnn")
        return False


# 设置混合精度训练
def setup_mixed_precision():
    if tf.config.list_physical_devices('GPU'):
        try:
            tf.keras.mixed_precision.set_global_policy('mixed_float16')
            print("\n混合精度配置:")
            print("- 已启用mixed_float16策略")
            return True
        except Exception as e:
            print("\n设置混合精度失败:", str(e))
            return False
    return False


class DeepONetFreeze(tf.keras.Model):
    def __init__(self):
        super(DeepONetFreeze, self).__init__()

        # Branch网络 - 处理输入场
        self.branch = tf.keras.Sequential([
            tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same', input_shape=(None, None, 1)),
            tf.keras.layers.Conv2D(16, (3, 3), activation='relu', padding='same'),
            tf.keras.layers.Flatten(),
            tf.keras.layers.Dense(100, activation='relu'),
            tf.keras.layers.Dense(50)
        ])

        # Trunk网络 - 处理空间坐标
        self.trunk = tf.keras.Sequential([
            tf.keras.layers.Dense(100, activation='relu', input_shape=(2,)),
            tf.keras.layers.Dense(100, activation='relu'),
            tf.keras.layers.Dense(50, activation='linear')
        ])

    def call(self, inputs):
        # 解包输入
        field_input, coords = inputs

        # Branch网络处理场数据
        branch_output = self.branch(field_input)

        # Trunk网络处理坐标
        trunk_output = self.trunk(coords)

        # DeepONet核心运算
        return tf.tensordot(branch_output, trunk_output, axes=([1], [1]))


def load_freeze_data(data_dir, start_step=0, end_step=500):
    """加载FreezeV10生成的数据"""
    phi_data = []
    temp_data = []
    solute_data = []

    for i in range(start_step, end_step + 1):
        try:
            # 使用统一的文件命名格式
            phi_file = f"{data_dir}相场第{i}步.csv"
            temp_file = f"{data_dir}温度场第{i}步.csv"
            solute_file = f"{data_dir}浓度场第{i}步.csv"

            # 检查文件是否存在
            if not os.path.exists(phi_file):
                print(f"警告: 文件不存在 {phi_file}")
                continue
            if not os.path.exists(temp_file):
                print(f"警告: 文件不存在 {temp_file}")
                continue
            if not os.path.exists(solute_file):
                print(f"警告: 文件不存在 {solute_file}")
                continue

            # 加载相场、温度场和溶质场数据
            phi = pd.read_csv(phi_file, header=None, encoding='utf-8').values
            temp = pd.read_csv(temp_file, header=None, encoding='utf-8').values
            solute = pd.read_csv(solute_file, header=None, encoding='utf-8').values

            print(f"成功加载第{i}步数据: 相场形状{phi.shape}, 温度场形状{temp.shape}, 浓度场形状{solute.shape}")

            phi_data.append(phi)
            temp_data.append(temp)
            solute_data.append(solute)
        except Exception as e:
            print(f"加载第{i}步数据时出错: {str(e)}")

    if not phi_data or not temp_data or not solute_data:
        raise ValueError("未能加载任何数据，请检查文件路径和格式")

    return np.array(phi_data), np.array(temp_data), np.array(solute_data)


def prepare_training_data(fields, dx=1e-7, dy=1e-7):
    """准备训练数据，采用多种方式生成更多样本"""
    # 检查数据形状
    if len(fields) < 2:
        raise ValueError("至少需要两个时间步的数据来创建输入-输出对")

    print(f"准备数据: 有{len(fields)}个时间步，每个场的形状为{fields[0].shape}")

    # 生成坐标网格
    m, n = fields[0].shape
    x = np.linspace(0, m * dx, m)
    y = np.linspace(0, n * dy, n)
    X, Y = np.meshgrid(x, y)
    coords = np.stack([X.flatten(), Y.flatten()], axis=-1)  # 形状: (m*n, 2)

    # 准备输入-输出对
    X_train = []
    y_train = []

    # 每个时间步作为输入，下一步作为输出
    for i in range(len(fields) - 1):
        # 将场数据重塑为合适的形状 (batch_size, height, width, channels)
        input_field = fields[i].reshape(-1, fields[i].shape[0], fields[i].shape[1], 1)

        # 添加到训练数据中
        X_train.append(input_field)
        y_train.append(fields[i + 1].flatten())  # 将输出平铺为一维向量

    # 如果样本太少，可以使用数据增强技术生成更多样本
    if len(X_train) < 5 and len(fields) >= 3:
        print("样本数量不足，使用数据增强生成更多样本...")

        # 使用两个时间步作为输入预测第三个时间步
        for i in range(len(fields) - 2):
            # 组合相邻两个时间步作为输入特征
            combined_field = np.concatenate([
                fields[i].reshape(-1, fields[i].shape[0], fields[i].shape[1], 1),
                fields[i + 1].reshape(-1, fields[i + 1].shape[0], fields[i + 1].shape[1], 1)
            ], axis=-1)  # [batch_size, height, width, 2]

            # 转换为单通道
            combined_field = np.mean(combined_field, axis=-1, keepdims=True)  # [batch_size, height, width, 1]

            X_train.append(combined_field)
            y_train.append(fields[i + 2].flatten())

    print(f"准备完成: 生成了{len(X_train)}个训练样本")

    # 将数据转换为NumPy数组
    X_branch = np.array(X_train)
    y_train = np.array(y_train)

    # 确保X_branch的形状正确 (n_samples, height, width, channels)
    if len(X_branch.shape) > 4:
        X_branch = X_branch.squeeze(1)  # 移除多余的维度

    print(f"X_branch shape: {X_branch.shape}")
    print(f"y_train shape: {y_train.shape}")

    return X_branch, coords, y_train


def visualize_prediction(true_field, pred_field, title):
    """可视化预测结果"""
    # 将预测结果重塑回原始场的形状
    m, n = true_field.shape
    pred_field_reshaped = pred_field.reshape(m, n)

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 5))

    im1 = ax1.imshow(true_field, cmap='jet')
    ax1.set_title('ground truth field')
    plt.colorbar(im1, ax=ax1)

    im2 = ax2.imshow(pred_field_reshaped, cmap='jet')
    ax2.set_title('predicted field')
    plt.colorbar(im2, ax=ax2)

    plt.suptitle(title)
    plt.savefig(f'{title}.png')
    plt.close()


def create_model(input_shape, num_points, output_dim):
    """创建简化版DeepONet模型"""
    # 指定在GPU上创建模型
    with tf.device('/GPU:0'):
        # 输入层
        field_input = tf.keras.layers.Input(shape=input_shape)

        # Branch网络 - 处理输入场
        x = tf.keras.layers.Conv2D(32, (3, 3), activation='relu', padding='same')(field_input)
        x = tf.keras.layers.Conv2D(16, (3, 3), activation='relu', padding='same')(x)
        x = tf.keras.layers.Flatten()(x)
        x = tf.keras.layers.Dense(100, activation='relu')(x)
        x = tf.keras.layers.Dense(50, activation='relu')(x)
        branch_output = tf.keras.layers.Dense(output_dim)(x)

        # 创建模型
        model = tf.keras.Model(inputs=field_input, outputs=branch_output)

    # 配置优化器
    optimizer = tf.keras.optimizers.Adam(learning_rate=0.001)
    if tf.config.list_physical_devices('GPU'):
        # 在GPU上使用混合精度
        optimizer = tf.keras.mixed_precision.LossScaleOptimizer(optimizer)

    return model


def parse_args():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='相变模拟训练参数')

    # 添加训练轮数参数
    parser.add_argument('--phi_epochs', type=int, default=100,
                        help='相场模型训练轮数 (默认: 100)')
    parser.add_argument('--temp_epochs', type=int, default=100,
                        help='温度场模型训练轮数 (默认: 100)')
    parser.add_argument('--solute_epochs', type=int, default=100,
                        help='浓度场模型训练轮数 (默认: 100)')

    # 添加批次大小参数
    parser.add_argument('--batch_size', type=int, default=2,
                        help='训练批次大小 (默认: 1)')

    # 添加数据路径参数
    parser.add_argument('--data_dir', type=str, default='./dataset/',
                        help='数据目录路径 (默认: 当前目录)')

    # 添加模型保存/加载参数
    parser.add_argument('--model_dir', type=str, default='./models',
                        help='模型保存/加载目录 (默认: ./models)')
    parser.add_argument('--load_models', action='store_true',
                        help='是否加载已有模型而不重新训练 (默认: False)')
    parser.add_argument('--save_models', action='store_false',
                        help='是否保存训练后的模型 (默认: False)')

    # 添加早停控制参数
    parser.add_argument('--use_early_stopping', action='store_true',
                        help='是否使用早停机制 (默认: False)')
    parser.add_argument('--patience', type=int, default=20,
                        help='早停耐心值，即多少轮loss不下降后停止训练 (默认: 20)')

    return parser.parse_args()


def main():
    # 解析命令行参数
    args = parse_args()

    # 设置数据路径
    data_dir = args.data_dir
    model_dir = args.model_dir

    # 创建模型保存目录（如果不存在）
    if args.save_models and not os.path.exists(model_dir):
        os.makedirs(model_dir)
        print(f"创建模型保存目录: {model_dir}")

    # 加载数据
    print("正在加载数据...")
    try:
        phi_data, temp_data, solute_data = load_freeze_data(data_dir)
        print(f"加载完成: {len(phi_data)}个相场, {len(temp_data)}个温度场, {len(solute_data)}个浓度场")
    except Exception as e:
        print(f"加载数据失败: {str(e)}")
        return

    # 准备训练数据
    print("正在准备训练数据...")
    try:
        # 使用修改后的数据准备函数
        X_phi_branch, coords_phi, y_phi = prepare_training_data(phi_data)
        X_temp_branch, coords_temp, y_temp = prepare_training_data(temp_data)
        X_solute_branch, coords_solute, y_solute = prepare_training_data(solute_data)

        # 打印数据形状
        print(f"相场分支输入形状: {X_phi_branch.shape}")
        print(f"相场输出形状: {y_phi.shape}")
        print(f"坐标形状: {coords_phi.shape}")
    except Exception as e:
        print(f"准备训练数据失败: {str(e)}")
        return

    # 从命令行参数获取训练参数
    phi_epochs = args.phi_epochs  # 相场训练轮数
    temp_epochs = args.temp_epochs  # 温度场训练轮数
    solute_epochs = args.solute_epochs  # 浓度场训练轮数
    batch_size = args.batch_size

    # 打印训练参数
    print(f"\n训练参数设置:")
    print(f"相场训练轮数: {phi_epochs}")
    print(f"温度场训练轮数: {temp_epochs}")
    print(f"浓度场训练轮数: {solute_epochs}")
    print(f"批次大小: {batch_size}")
    print(f"模型保存: {'是' if args.save_models else '否'}")
    print(f"加载已有模型: {'是' if args.load_models else '否'}")
    print(f"使用早停机制: {'是' if args.use_early_stopping else '否'}")
    if args.use_early_stopping:
        print(f"早停耐心值: {args.patience}")
    print()

    # 设置早停回调函数（如果需要的话）
    callbacks = []
    if args.use_early_stopping:
        early_stopping = tf.keras.callbacks.EarlyStopping(
            monitor='loss',
            patience=args.patience,
            restore_best_weights=True
        )
        callbacks.append(early_stopping)

    # 定义模型文件路径
    phi_model_path = os.path.join(model_dir, "phi_model.keras")
    temp_model_path = os.path.join(model_dir, "temp_model.keras")
    solute_model_path = os.path.join(model_dir, "solute_model.keras")

    # 创建或加载模型
    phi_model = None
    temp_model = None
    solute_model = None

    # 相场模型
    if args.load_models and os.path.exists(phi_model_path):
        print(f"加载已有相场模型: {phi_model_path}")
        phi_model = tf.keras.models.load_model(phi_model_path)
        phi_history = None
    else:
        # 创建和训练相场模型
        print(f"训练相场模型 (epochs: {phi_epochs})...")
        phi_model = create_model(
            input_shape=X_phi_branch[0].shape,
            num_points=coords_phi.shape[0],
            output_dim=y_phi[0].shape[0]
        )
        phi_model.compile(optimizer='adam', loss='mse')

        phi_history = phi_model.fit(
            X_phi_branch, y_phi,
            epochs=phi_epochs,  # 使用相场专用的训练轮数
            batch_size=batch_size,
            callbacks=callbacks,  # 使用设置的回调函数列表
            verbose=1
        )

        # 保存模型
        if args.save_models:
            print(f"保存相场模型到: {phi_model_path}")
            phi_model.save(phi_model_path)

    # 温度场模型
    if args.load_models and os.path.exists(temp_model_path):
        print(f"加载已有温度场模型: {temp_model_path}")
        temp_model = tf.keras.models.load_model(temp_model_path)
        temp_history = None
    else:
        # 创建和训练温度场模型
        print(f"训练温度场模型 (epochs: {temp_epochs})...")
        temp_model = create_model(
            input_shape=X_temp_branch[0].shape,
            num_points=coords_temp.shape[0],
            output_dim=y_temp[0].shape[0]
        )
        temp_model.compile(optimizer='adam', loss='mse')

        temp_history = temp_model.fit(
            X_temp_branch, y_temp,
            epochs=temp_epochs,  # 使用温度场专用的训练轮数
            batch_size=batch_size,
            callbacks=callbacks,  # 使用设置的回调函数列表
            verbose=1
        )

        # 保存模型
        if args.save_models:
            print(f"保存温度场模型到: {temp_model_path}")
            temp_model.save(temp_model_path)

    # 浓度场模型
    if args.load_models and os.path.exists(solute_model_path):
        print(f"加载已有浓度场模型: {solute_model_path}")
        solute_model = tf.keras.models.load_model(solute_model_path)
        solute_history = None
    else:
        # 创建和训练浓度场模型
        print(f"训练浓度场模型 (epochs: {solute_epochs})...")
        solute_model = create_model(
            input_shape=X_solute_branch[0].shape,
            num_points=coords_solute.shape[0],
            output_dim=y_solute[0].shape[0]
        )
        solute_model.compile(optimizer='adam', loss='mse')

        solute_history = solute_model.fit(
            X_solute_branch, y_solute,
            epochs=solute_epochs,  # 使用浓度场专用的训练轮数
            batch_size=batch_size,
            callbacks=callbacks,  # 使用设置的回调函数列表
            verbose=1
        )

        # 保存模型
        if args.save_models:
            print(f"保存浓度场模型到: {solute_model_path}")
            solute_model.save(solute_model_path)

    # 预测并可视化结果
    print("正在进行预测和可视化...")

    # 使用最后一个时间步数据进行预测
    phi_pred = phi_model.predict(X_phi_branch[-1:], verbose=1)[0]
    temp_pred = temp_model.predict(X_temp_branch[-1:], verbose=1)[0]
    solute_pred = solute_model.predict(X_solute_branch[-1:], verbose=1)[0]

    # 可视化预测结果
    visualize_prediction(phi_data[-1], phi_pred, "phase field")
    visualize_prediction(temp_data[-1], temp_pred, "temperature field")
    visualize_prediction(solute_data[-1], solute_pred, "concentration field")

    # 绘制训练历史
    if not args.load_models:
        plt.figure(figsize=(12, 4))

        plt.subplot(131)
        if phi_history:
            plt.plot(phi_history.history['loss'], label='phase field-loss')
            plt.title('phase field-loss')
            plt.xlabel('epochs')
            plt.ylabel('loss')
            plt.legend()

        plt.subplot(132)
        if temp_history:
            plt.plot(temp_history.history['loss'], label='temperature field-loss')
            plt.title('temperature field-loss')
            plt.xlabel('epochs')
            plt.ylabel('loss')
            plt.legend()

        plt.subplot(133)
        if solute_history:
            plt.plot(solute_history.history['loss'], label='concentration field-loss')
            plt.title('concentration field-loss')
            plt.xlabel('epochs')
            plt.ylabel('loss')
            plt.legend()

        plt.tight_layout()
        plt.savefig('training_history.png')
        print("训练历史已保存为 'training_history.png'")
        plt.close()

    print("训练和预测完成!")


if __name__ == "__main__":
    # 配置GPU
    gpu_available = setup_gpu()

    # 配置混合精度
    if gpu_available:
        mixed_precision_enabled = setup_mixed_precision()

    # 继续执行主程序
    main()