import os
import argparse
import time
from ch_solver import CHSolver
from deeponet_ch import train_model
from visualization import visualize_predictions

def main():
    """主函数，运行完整流程"""
    parser = argparse.ArgumentParser(description='DeepONet求解二维Cahn-Hilliard方程')
    
    parser.add_argument('--mode', type=str, default='all', 
                        choices=['data', 'train', 'visualize', 'all'],
                        help='运行模式: 生成数据、训练模型、可视化结果或全部')
    
    parser.add_argument('--nx', type=int, default=32, help='x方向网格点数')
    parser.add_argument('--ny', type=int, default=32, help='y方向网格点数')
    parser.add_argument('--epsilon', type=float, default=0.1, help='界面宽度参数')
    parser.add_argument('--dt', type=float, default=1e-4, help='时间步长')
    
    parser.add_argument('--n_samples', type=int, default=3, help='训练样本数量')
    parser.add_argument('--n_steps', type=int, default=10, help='每个样本的时间步数')
    parser.add_argument('--save_interval', type=int, default=10, help='保存间隔')
    
    parser.add_argument('--epochs', type=int, default=100, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=32, help='批量大小')
    
    parser.add_argument('--data_path', type=str, default='ch_dataset.npz', help='数据集路径')
    parser.add_argument('--model_path', type=str, default='models/deeponet_best.h5', help='模型路径')
    
    args = parser.parse_args()
    
    # 创建需要的目录
    os.makedirs('models', exist_ok=True)
    os.makedirs('results', exist_ok=True)
    
    # 根据模式运行
    if args.mode in ['data', 'all']:
        print("===== 第1步: 生成数据集 =====")
        generate_data(args)
    
    if args.mode in ['train', 'all']:
        print("\n===== 第2步: 训练DeepONet模型 =====")
        train_deeponet(args)
    
    if args.mode in ['visualize', 'all']:
        print("\n===== 第3步: 可视化结果 =====")
        visualize_results(args)
    
    print("\n全部完成!")

def generate_data(args):
    """生成数据集"""
    start_time = time.time()
    
    # 创建求解器
    solver = CHSolver(nx=args.nx, ny=args.ny, dt=args.dt, epsilon=args.epsilon)
    
    # 生成数据集
    print(f"生成 {args.n_samples} 个样本，每个样本 {args.n_steps} 个时间步...")
    X_u, X_y, y = solver.generate_dataset(
        n_samples=args.n_samples,
        n_steps=args.n_steps,
        save_interval=args.save_interval
    )
    
    # 保存数据集
    solver.save_dataset(args.data_path, X_u, X_y, y)
    
    print(f"数据生成完成，耗时: {time.time() - start_time:.2f}秒")
    print(f"数据集形状: X_u: {X_u.shape}, X_y: {X_y.shape}, y: {y.shape}")

def train_deeponet(args):
    """训练DeepONet模型"""
    start_time = time.time()
    
    # 检查数据集是否存在
    if not os.path.exists(args.data_path):
        print(f"错误: 找不到数据集文件 {args.data_path}，请先生成数据集")
        return
    
    # 训练模型
    model, history, _ = train_model(
        data_path=args.data_path, 
        epochs=args.epochs, 
        batch_size=args.batch_size
    )
    
    print(f"模型训练完成，耗时: {time.time() - start_time:.2f}秒")

def visualize_results(args):
    """可视化结果"""
    start_time = time.time()
    
    # 检查模型是否存在
    if not os.path.exists(args.model_path):
        print(f"错误: 找不到模型文件 {args.model_path}，请先训练模型")
        return
    
    # 可视化预测结果
    visualize_predictions(
        model_path=args.model_path,
        nx=args.nx,
        ny=args.ny,
        n_steps=100,  # 测试案例的时间步数
        save_interval=10  # 测试案例的保存间隔
    )
    
    print(f"可视化完成，耗时: {time.time() - start_time:.2f}秒")

if __name__ == "__main__":
    main() 