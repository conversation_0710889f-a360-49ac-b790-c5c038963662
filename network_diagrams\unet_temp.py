import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle, FancyArrowPatch, PathPatch
from matplotlib.path import Path
import matplotlib.colors as mcolors

# 创建画布
fig, ax = plt.subplots(figsize=(14, 10), facecolor='white')

# 设置颜色
colors = {
    'input': mcolors.to_rgb('#C8E6C9'),  # 浅绿色
    'conv': mcolors.to_rgb('#81D4FA'),   # 浅蓝色
    'pool': mcolors.to_rgb('#FFCC80'),   # 浅橙色
    'bn': mcolors.to_rgb('#E1BEE7'),     # 浅紫色
    'relu': mcolors.to_rgb('#FFECB3'),   # 浅黄色
    'gap': mcolors.to_rgb('#EF9A9A'),    # 浅红色
    'dense': mcolors.to_rgb('#B39DDB'),  # 淡紫色
    'output': mcolors.to_rgb('#F48FB1'),  # 粉色
    'attention': mcolors.to_rgb('#B2DFDB'),  # 蓝绿色
    'up': mcolors.to_rgb('#B2EBF2'),     # 浅蓝绿色
    'concat': mcolors.to_rgb('#F0F4C3'),  # 浅黄绿色
    'leaky': mcolors.to_rgb('#DCEDC8'),  # 浅绿黄色
    'bottleneck': mcolors.to_rgb('#D7CCC8')  # 浅棕色
}

# 绘制基本框架
def draw_box(x, y, width, height, color, label, fontsize=9):
    rect = Rectangle((x, y), width, height, linewidth=1, edgecolor='black', facecolor=color, alpha=0.8)
    ax.add_patch(rect)
    ax.text(x + width/2, y + height/2, label, ha='center', va='center', fontsize=fontsize, fontweight='bold')

def draw_arrow(x1, y1, x2, y2, style='normal', color='black'):
    if style == 'normal':
        arrow = FancyArrowPatch((x1, y1), (x2, y2), connectionstyle="arc3,rad=0.0", 
                               arrowstyle='->', mutation_scale=15, linewidth=1.5, color=color)
    elif style == 'skip':
        arrow = FancyArrowPatch((x1, y1), (x2, y2), connectionstyle="arc3,rad=0.3", 
                               arrowstyle='->', mutation_scale=15, linewidth=1.5, color=color)
    ax.add_patch(arrow)

# 绘制UNet结构
# 输入层
draw_box(1, 9, 2, 1, colors['input'], "Input\n(h, w, 1)")

# 编码器第一层
draw_box(4, 9, 2, 0.8, colors['conv'], "Conv2D\n3x3, 64")
draw_arrow(3, 9.5, 4, 9.5)
draw_box(4, 8, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(5, 9, 5, 8.6)
draw_box(4, 7.2, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(5, 7.2, 5, 6.8)

draw_box(4, 6, 2, 0.8, colors['conv'], "Conv2D\n3x3, 64")
draw_arrow(5, 7.2, 5, 6.8)
draw_box(4, 5, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(5, 6, 5, 5.6)
draw_box(4, 4.2, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(5, 4.2, 5, 3.8)

# 池化层1
draw_box(4, 3, 2, 0.8, colors['pool'], "MaxPool\n2x2")
draw_arrow(5, 4.2, 5, 3.8)

# 编码器第二层
draw_box(7, 9, 2, 0.8, colors['conv'], "Conv2D\n3x3, 128")
draw_arrow(6, 3.4, 7, 9.4)
draw_box(7, 8, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(8, 9, 8, 8.6)
draw_box(7, 7.2, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(8, 7.2, 8, 6.8)

draw_box(7, 6, 2, 0.8, colors['conv'], "Conv2D\n3x3, 128")
draw_arrow(8, 7.2, 8, 6.8)
draw_box(7, 5, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(8, 6, 8, 5.6)
draw_box(7, 4.2, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(8, 4.2, 8, 3.8)

# 池化层2
draw_box(7, 3, 2, 0.8, colors['pool'], "MaxPool\n2x2")
draw_arrow(8, 4.2, 8, 3.8)

# 编码器第三层
draw_box(10, 9, 2, 0.8, colors['conv'], "Conv2D\n3x3, 256")
draw_arrow(9, 3.4, 10, 9.4)
draw_box(10, 8, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(11, 9, 11, 8.6)
draw_box(10, 7.2, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(11, 7.2, 11, 6.8)

draw_box(10, 6, 2, 0.8, colors['conv'], "Conv2D\n3x3, 256")
draw_arrow(11, 7.2, 11, 6.8)
draw_box(10, 5, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(11, 6, 11, 5.6)
draw_box(10, 4.2, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(11, 4.2, 11, 3.8)

# 池化层3
draw_box(10, 3, 2, 0.8, colors['pool'], "MaxPool\n2x2")
draw_arrow(11, 4.2, 11, 3.8)

# 瓶颈层
draw_box(13, 6, 2, 1, colors['bottleneck'], "Bottleneck\nConv2D 3x3\nConv2D 3x3\n512 filters")
draw_arrow(12, 3.4, 13, 6.5)

# 注意力机制
draw_box(13, 4, 2, 1.2, colors['attention'], "Attention\nChannel+Spatial\nAttention")
draw_arrow(14, 6, 14, 5.2)

# 上采样层3
draw_box(16, 9, 2, 0.8, colors['up'], "ConvTranspose\n2x2, 256")
draw_arrow(15, 4.5, 16, 9.4)

# Skip连接3
draw_box(16, 8, 2, 0.8, colors['concat'], "Concat")
draw_arrow(12, 7.5, 16, 8.4, style='skip', color='blue')
draw_arrow(17, 9, 17, 8.8)

# 解码器第三层
draw_box(16, 7, 2, 0.8, colors['conv'], "Conv2D\n3x3, 256")
draw_arrow(17, 8, 17, 7.8)
draw_box(16, 6, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(17, 7, 17, 6.6)
draw_box(16, 5.2, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(17, 5.2, 17, 4.8)

draw_box(16, 4, 2, 0.8, colors['conv'], "Conv2D\n3x3, 256")
draw_arrow(17, 5.2, 17, 4.8)
draw_box(16, 3, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(17, 4, 17, 3.6)
draw_box(16, 2.2, 2, 0.6, colors['bn'], "BatchNorm")

# 上采样层2
draw_box(19, 9, 2, 0.8, colors['up'], "ConvTranspose\n2x2, 128")
draw_arrow(18, 2.5, 19, 9.4)

# Skip连接2
draw_box(19, 8, 2, 0.8, colors['concat'], "Concat")
draw_arrow(9, 7.5, 19, 8.4, style='skip', color='blue')
draw_arrow(20, 9, 20, 8.8)

# 解码器第二层
draw_box(19, 7, 2, 0.8, colors['conv'], "Conv2D\n3x3, 128")
draw_arrow(20, 8, 20, 7.8)
draw_box(19, 6, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(20, 7, 20, 6.6)
draw_box(19, 5.2, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(20, 5.2, 20, 4.8)

draw_box(19, 4, 2, 0.8, colors['conv'], "Conv2D\n3x3, 128")
draw_arrow(20, 5.2, 20, 4.8)
draw_box(19, 3, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(20, 4, 20, 3.6)
draw_box(19, 2.2, 2, 0.6, colors['bn'], "BatchNorm")

# 上采样层1
draw_box(22, 9, 2, 0.8, colors['up'], "ConvTranspose\n2x2, 64")
draw_arrow(21, 2.5, 22, 9.4)

# Skip连接1
draw_box(22, 8, 2, 0.8, colors['concat'], "Concat")
draw_arrow(6, 7.5, 22, 8.4, style='skip', color='blue')
draw_arrow(23, 9, 23, 8.8)

# 解码器第一层
draw_box(22, 7, 2, 0.8, colors['conv'], "Conv2D\n3x3, 64")
draw_arrow(23, 8, 23, 7.8)
draw_box(22, 6, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(23, 7, 23, 6.6)
draw_box(22, 5.2, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(23, 5.2, 23, 4.8)

draw_box(22, 4, 2, 0.8, colors['conv'], "Conv2D\n3x3, 64")
draw_arrow(23, 5.2, 23, 4.8)
draw_box(22, 3, 2, 0.6, colors['leaky'], "LeakyReLU")
draw_arrow(23, 4, 23, 3.6)
draw_box(22, 2.2, 2, 0.6, colors['bn'], "BatchNorm")

# 多尺度特征融合
draw_box(25, 6, 2.5, 2, colors['gap'], "Multi-Scale Feature\nFusion: GAP→Concat\n(bottleneck+conv6+\nconv7+conv8)")
draw_arrow(24, 2.5, 25, 7)
draw_arrow(15, 6.5, 25, 7, style='skip', color='green')
draw_arrow(18, 4.5, 25, 6.8, style='skip', color='green')
draw_arrow(21, 4.5, 25, 6.6, style='skip', color='green')

# 全连接层
draw_box(25, 3.5, 2.5, 1, colors['dense'], "Dense(1024)\nLeakyReLU+BN\n+Dropout(0.5)")
draw_arrow(26.2, 6, 26.2, 4.5)

draw_box(25, 2, 2.5, 1, colors['dense'], "Dense(512)\nLeakyReLU+BN\n+Dropout(0.4)")
draw_arrow(26.2, 3.5, 26.2, 3)

# 输出层
draw_box(25, 0.5, 2.5, 1, colors['output'], "Output\nDense(output_dim)")
draw_arrow(26.2, 2, 26.2, 1.5)

# 图例
legend_x = 1
legend_y = 2
legend_height = 0.3
legend_width = 1.5
legend_spacing = 0.4

for i, (key, color) in enumerate(colors.items()):
    row = i % 8
    col = i // 8
    x_pos = legend_x + col * 3
    y_pos = legend_y - row * legend_spacing
    draw_box(x_pos, y_pos, legend_width, legend_height, color, key.capitalize(), fontsize=8)

# 设置坐标轴
ax.set_xlim(0, 28)
ax.set_ylim(0, 11)
ax.axis('off')

# 标题
plt.title('Temperature Field - UNet Architecture', fontsize=20, fontweight='bold', y=0.98)

# 保存图像
plt.savefig('unet_temp_field.png', dpi=300, bbox_inches='tight')
plt.close()

print("Temperature field UNet architecture saved as 'unet_temp_field.png'") 