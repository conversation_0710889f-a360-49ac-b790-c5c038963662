import matplotlib.pyplot as plt
import numpy as np
from matplotlib.patches import Rectangle, FancyArrowPatch, PathPatch
from matplotlib.path import Path
import matplotlib.colors as mcolors

# 创建画布
fig, ax = plt.subplots(figsize=(12, 8), facecolor='white')

# 设置颜色
colors = {
    'input': mcolors.to_rgb('#C8E6C9'),  # 浅绿色
    'conv': mcolors.to_rgb('#81D4FA'),   # 浅蓝色
    'pool': mcolors.to_rgb('#FFCC80'),   # 浅橙色
    'bn': mcolors.to_rgb('#E1BEE7'),     # 浅紫色
    'relu': mcolors.to_rgb('#FFECB3'),   # 浅黄色
    'gap': mcolors.to_rgb('#EF9A9A'),    # 浅红色
    'dense': mcolors.to_rgb('#B39DDB'),  # 淡紫色
    'output': mcolors.to_rgb('#F48FB1'),  # 粉色
    'attention': mcolors.to_rgb('#B2DFDB'),  # 蓝绿色
    'add': mcolors.to_rgb('#CFD8DC'),    # 浅灰色
}

# 绘制基本框架
def draw_box(x, y, width, height, color, label, fontsize=9):
    rect = Rectangle((x, y), width, height, linewidth=1, edgecolor='black', facecolor=color, alpha=0.8)
    ax.add_patch(rect)
    ax.text(x + width/2, y + height/2, label, ha='center', va='center', fontsize=fontsize, fontweight='bold')

def draw_arrow(x1, y1, x2, y2, style='normal'):
    if style == 'normal':
        arrow = FancyArrowPatch((x1, y1), (x2, y2), connectionstyle="arc3,rad=0.0", 
                               arrowstyle='->', mutation_scale=15, linewidth=1.5, color='black')
    elif style == 'skip':
        arrow = FancyArrowPatch((x1, y1), (x2, y2), connectionstyle="arc3,rad=0.3", 
                               arrowstyle='->', mutation_scale=15, linewidth=1.5, color='red')
    ax.add_patch(arrow)

# 绘制ResNet结构
# 输入层
draw_box(1, 8, 2, 1, colors['input'], "Input\n(h, w, 1)")

# 初始卷积
draw_box(5, 8, 2, 1, colors['conv'], "Conv2D\n7x7, 32")
draw_arrow(3, 8.5, 5, 8.5)
draw_box(5, 7, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(6, 7, 6, 6.6)
draw_box(5, 6, 2, 0.6, colors['relu'], "ReLU")
draw_arrow(6, 6, 6, 5.6)
draw_box(5, 5, 2, 0.6, colors['pool'], "MaxPool\n3x3")

# 残差块1
draw_box(9, 8, 2, 0.6, colors['conv'], "Conv2D\n3x3, 32")
draw_arrow(7, 5.3, 9, 8.3)
draw_box(9, 7, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(10, 7, 10, 6.6)
draw_box(9, 6, 2, 0.6, colors['relu'], "ReLU")
draw_arrow(10, 6, 10, 5.6)
draw_box(9, 5, 2, 0.6, colors['conv'], "Conv2D\n3x3, 32")
draw_arrow(10, 5, 10, 4.6)
draw_box(9, 4, 2, 0.6, colors['bn'], "BatchNorm")

# 残差连接
draw_arrow(7, 5.3, 10, 4.3, style='skip')
draw_box(9, 3, 2, 0.6, colors['add'], "Add")
draw_arrow(10, 4, 10, 3.6)
draw_box(9, 2, 2, 0.6, colors['relu'], "ReLU")
draw_arrow(10, 2, 10, 1.6)

# 残差块2
draw_box(13, 8, 2, 0.6, colors['conv'], "Conv2D\n3x3, 64")
draw_arrow(11, 2.3, 13, 8.3)
draw_box(13, 7, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(14, 7, 14, 6.6)
draw_box(13, 6, 2, 0.6, colors['relu'], "ReLU")
draw_arrow(14, 6, 14, 5.6)
draw_box(13, 5, 2, 0.6, colors['conv'], "Conv2D\n3x3, 64")
draw_arrow(14, 5, 14, 4.6)
draw_box(13, 4, 2, 0.6, colors['bn'], "BatchNorm")

# 残差连接 (投影)
draw_box(13, 2.5, 2, 0.6, colors['conv'], "Conv2D\n1x1, 64")
draw_arrow(11, 2.3, 13, 2.8, style='skip')
draw_box(13, 3.2, 2, 0.6, colors['bn'], "BatchNorm")
draw_arrow(14, 3.2, 14, 3.8)

# 添加
draw_box(13, 1.8, 2, 0.6, colors['add'], "Add")
draw_arrow(14, 2.5, 14, 2.4)
draw_arrow(14, 4, 14, 2.4)
draw_box(13, 1, 2, 0.6, colors['relu'], "ReLU")
draw_arrow(14, 1.8, 14, 1.6)

# 注意力机制
draw_box(17, 6, 2.5, 1.5, colors['attention'], "Channel Attention\nGAP→Dense→Dense\nReshape→Multiply")
draw_arrow(15, 1.3, 17, 6.5)
draw_box(17, 4, 2.5, 1.5, colors['attention'], "Spatial Attention\nConv2D 7x7\nActivation→Multiply")
draw_arrow(17.5, 6, 17.5, 5.5)

# 全局特征提取
draw_box(17, 2, 2.5, 1, colors['gap'], "GlobalAvgPool")
draw_arrow(17.5, 4, 17.5, 3)
draw_box(17, 1, 2.5, 0.6, colors['dense'], "Dense(256)")
draw_arrow(17.5, 2, 17.5, 1.6)

# 输出层
draw_box(21, 1, 2, 0.6, colors['output'], "Output")
draw_arrow(19.5, 1.3, 21, 1.3)

# 图例
legend_x = 1
legend_y = 1
legend_height = 0.4
legend_width = 1.5
legend_spacing = 0.5

for i, (key, color) in enumerate(colors.items()):
    y_pos = legend_y - i * legend_spacing
    draw_box(legend_x, y_pos, legend_width, legend_height, color, key.capitalize())

# 设置坐标轴
ax.set_xlim(0, 24)
ax.set_ylim(0, 10)
ax.axis('off')

# 标题
plt.title('Phase Field - ResNet Architecture', fontsize=18, fontweight='bold', y=0.98)

# 保存图像 - 使用Windows路径格式
plt.savefig('resnet_phase_field.png', dpi=300, bbox_inches='tight')
plt.close()

print("相场模型ResNet架构图已保存为 'resnet_phase_field.png'") 