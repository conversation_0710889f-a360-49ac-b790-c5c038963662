import os
import re

def rename_files():
    # 获取当前目录下所有文件
    files = os.listdir('.')
    
    # 遍历文件列表
    for file in files:
        # 检查文件名中是否包含多个连续空格
        if '  ' in file:
            # 使用正则表达式将多个连续空格替换为单个空格
            new_name = re.sub(r'\s+', '', file)
            
            # 重命名文件
            os.rename(file, new_name)
            
            print(f'将 "{file}" 重命名为 "{new_name}"')

if __name__ == "__main__":
    rename_files()
    print("文件重命名完成！") 