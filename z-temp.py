import argparse
import os
import pickle
import numpy as np
import tensorflow as tf
from utils import (
    clear_memory, setup_gpu, setup_mixed_precision, load_temp_data,
    prepare_training_data, visualize_prediction, compare_fields,
    predict_with_original_resolution, generate_multi_step_predictions,
    GPUMonitor, TempFieldMonitor
)

# ========== 温度场模型结构 ==========
def create_temp_model(input_shape, output_dim, use_cosine_decay=False):
    with tf.device('/GPU:0'):
        field_input = tf.keras.layers.Input(shape=input_shape)
        # UNet风格结构 + 注意力
        conv1 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(field_input)
        conv1 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv1)
        conv1 = tf.keras.layers.BatchNormalization()(conv1)
        conv1 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(conv1)
        conv1 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv1)
        conv1 = tf.keras.layers.BatchNormalization()(conv1)
        pool1 = tf.keras.layers.MaxPooling2D((2, 2))(conv1)
        conv2 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(pool1)
        conv2 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv2)
        conv2 = tf.keras.layers.BatchNormalization()(conv2)
        conv2 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(conv2)
        conv2 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv2)
        conv2 = tf.keras.layers.BatchNormalization()(conv2)
        pool2 = tf.keras.layers.MaxPooling2D((2, 2))(conv2)
        conv3 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(pool2)
        conv3 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv3)
        conv3 = tf.keras.layers.BatchNormalization()(conv3)
        conv3 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(conv3)
        conv3 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv3)
        conv3 = tf.keras.layers.BatchNormalization()(conv3)
        pool3 = tf.keras.layers.MaxPooling2D((2, 2))(conv3)
        bottleneck = tf.keras.layers.Conv2D(512, (3, 3), activation=None, padding='same')(pool3)
        bottleneck = tf.keras.layers.LeakyReLU(alpha=0.2)(bottleneck)
        bottleneck = tf.keras.layers.BatchNormalization()(bottleneck)
        bottleneck = tf.keras.layers.Conv2D(512, (3, 3), activation=None, padding='same')(bottleneck)
        bottleneck = tf.keras.layers.LeakyReLU(alpha=0.2)(bottleneck)
        bottleneck = tf.keras.layers.BatchNormalization()(bottleneck)
        # 通道注意力
        se = tf.keras.layers.GlobalAveragePooling2D()(bottleneck)
        se = tf.keras.layers.Dense(512 // 16, activation='relu')(se)
        se = tf.keras.layers.Dense(512, activation='sigmoid')(se)
        se = tf.keras.layers.Reshape((1, 1, 512))(se)
        bottleneck = tf.keras.layers.Multiply()([bottleneck, se])
        # 空间注意力
        spatial = tf.keras.layers.Conv2D(1, (7, 7), padding='same', activation='sigmoid')(bottleneck)
        bottleneck = tf.keras.layers.Multiply()([bottleneck, spatial])
        up3 = tf.keras.layers.Conv2DTranspose(256, (2, 2), strides=(2, 2), padding='same')(bottleneck)
        concat3 = tf.keras.layers.Concatenate()([up3, conv3])
        conv6 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(concat3)
        conv6 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv6)
        conv6 = tf.keras.layers.BatchNormalization()(conv6)
        conv6 = tf.keras.layers.Conv2D(256, (3, 3), activation=None, padding='same')(conv6)
        conv6 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv6)
        conv6 = tf.keras.layers.BatchNormalization()(conv6)
        up2 = tf.keras.layers.Conv2DTranspose(128, (2, 2), strides=(2, 2), padding='same')(conv6)
        concat2 = tf.keras.layers.Concatenate()([up2, conv2])
        conv7 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(concat2)
        conv7 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv7)
        conv7 = tf.keras.layers.BatchNormalization()(conv7)
        conv7 = tf.keras.layers.Conv2D(128, (3, 3), activation=None, padding='same')(conv7)
        conv7 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv7)
        conv7 = tf.keras.layers.BatchNormalization()(conv7)
        up1 = tf.keras.layers.Conv2DTranspose(64, (2, 2), strides=(2, 2), padding='same')(conv7)
        concat1 = tf.keras.layers.Concatenate()([up1, conv1])
        conv8 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(concat1)
        conv8 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv8)
        conv8 = tf.keras.layers.BatchNormalization()(conv8)
        conv8 = tf.keras.layers.Conv2D(64, (3, 3), activation=None, padding='same')(conv8)
        conv8 = tf.keras.layers.LeakyReLU(alpha=0.2)(conv8)
        conv8 = tf.keras.layers.BatchNormalization()(conv8)
        gap = tf.keras.layers.GlobalAveragePooling2D()(conv8)
        gap_bottleneck = tf.keras.layers.GlobalAveragePooling2D()(bottleneck)
        gap_conv6 = tf.keras.layers.GlobalAveragePooling2D()(conv6)
        gap_conv7 = tf.keras.layers.GlobalAveragePooling2D()(conv7)
        multi_scale = tf.keras.layers.Concatenate()([gap, gap_bottleneck, gap_conv6, gap_conv7])
        x = tf.keras.layers.Dense(1024, activation=None)(multi_scale)
        x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Dropout(0.5)(x)
        res = tf.keras.layers.Dense(1024)(multi_scale)
        x = tf.keras.layers.add([x, res])
        x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
        x = tf.keras.layers.Dense(512, activation=None)(x)
        x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.Dropout(0.4)(x)
        res2 = tf.keras.layers.Dense(512)(x)
        x = tf.keras.layers.Dense(512, activation=None)(x)
        x = tf.keras.layers.LeakyReLU(alpha=0.2)(x)
        x = tf.keras.layers.BatchNormalization()(x)
        x = tf.keras.layers.add([x, res2])
        branch_output = tf.keras.layers.Dense(output_dim)(x)
        model = tf.keras.Model(inputs=field_input, outputs=branch_output)
    if use_cosine_decay:
        initial_lr = 1e-4
        lr_schedule = tf.keras.optimizers.schedules.CosineDecay(
            initial_learning_rate=initial_lr,
            decay_steps=2000,
            alpha=1e-6
        )
        optimizer = tf.keras.optimizers.Adam(learning_rate=lr_schedule, clipnorm=0.5)
    else:
        optimizer = tf.keras.optimizers.Adam(learning_rate=1e-4, clipnorm=0.5)
    if tf.config.list_physical_devices('GPU'):
        optimizer = tf.keras.mixed_precision.LossScaleOptimizer(optimizer)
    def combined_loss(y_true, y_pred):
        y_true = tf.cast(y_true, tf.float32)
        y_pred = tf.cast(y_pred, tf.float32)
        mse = tf.keras.losses.MeanSquaredError()(y_true, y_pred)
        mae = tf.keras.losses.MeanAbsoluteError()(y_true, y_pred)
        huber = tf.keras.losses.Huber(delta=1.0)(y_true, y_pred)
        return 0.6 * mse + 0.2 * mae + 0.2 * huber
    model.compile(optimizer=optimizer, loss=combined_loss, metrics=['mae', 'mse'])
    return model

# ========== 参数解析 ==========
def parse_args():
    parser = argparse.ArgumentParser(description='温度场训练参数')
    parser.add_argument('--temp_data_dir', type=str, default='E:/Freezedata/TemperatureField_Data/', help='温度场数据目录')
    parser.add_argument('--model_dir', type=str, default='./models', help='模型保存目录')
    parser.add_argument('--results_dir', type=str, default='./models/results', help='结果保存目录')
    parser.add_argument('--epochs', type=int, default=2, help='训练轮数')
    parser.add_argument('--batch_size', type=int, default=2, help='批次大小')
    parser.add_argument('--stride', type=int, default=10, help='数据加载步长')
    parser.add_argument('--max_samples', type=int, default=5000, help='最大加载样本数')
    parser.add_argument('--start_step', type=int, default=0, help='起始步数')
    parser.add_argument('--end_step', type=int, default=3050, help='结束步数')
    parser.add_argument('--downsample', type=int, default=4, help='空间降采样因子')
    parser.add_argument('--use_data_augmentation', action='store_true', default=True, help='是否使用数据增强')
    parser.add_argument('--use_cosine_decay', action='store_true', default=False, help='是否使用余弦退火学习率')
    parser.add_argument('--load_model', action='store_true', help='是否加载已有模型')
    parser.add_argument('--save_model', action='store_true', help='是否保存模型')
    parser.add_argument('--generate_multi_step', action='store_true', help='是否生成多时间步预测图')
    parser.add_argument('--multi_step_start', type=int, default=0, help='多步预测起始步')
    parser.add_argument('--multi_step_end', type=int, default=3000, help='多步预测结束步')
    parser.add_argument('--multi_step_interval', type=int, default=500, help='多步预测间隔')

    # 新增监控和控制参数
    parser.add_argument('--enable_gpu_monitor', action='store_true', default=True, help='是否启用GPU监控')
    parser.add_argument('--enable_temp_monitor', action='store_true', default=True, help='是否启用温度场训练监控')
    parser.add_argument('--use_early_stopping', action='store_true', default=False, help='是否使用早停机制')
    parser.add_argument('--patience', type=int, default=20, help='早停耐心值')
    parser.add_argument('--min_delta', type=float, default=1e-4, help='早停最小改善值')
    parser.add_argument('--enable_detailed_memory', action='store_true', default=False, help='是否启用详细内存监控')
    parser.add_argument('--memory_check_interval', type=int, default=10, help='内存检查间隔(批次)')
    parser.add_argument('--enable_lr_scheduler', action='store_true', default=False, help='是否启用学习率调度器')
    parser.add_argument('--lr_reduce_factor', type=float, default=0.5, help='学习率衰减因子')
    parser.add_argument('--lr_reduce_patience', type=int, default=10, help='学习率衰减耐心值')
    return parser.parse_args()

# ========== 主流程 ==========
def main():
    args = parse_args()
    os.makedirs(args.model_dir, exist_ok=True)
    os.makedirs(args.results_dir, exist_ok=True)
    # 配置GPU
    gpu_available = setup_gpu()
    if gpu_available:
        setup_mixed_precision()
    # 加载数据
    print('加载温度场数据...')
    temp_data = load_temp_data(
        args.temp_data_dir,
        start_step=args.start_step, end_step=args.end_step,
        stride=args.stride, max_samples=args.max_samples
    )
    clear_memory()

    # 详细内存监控
    if args.enable_detailed_memory:
        import psutil
        memory_before = psutil.virtual_memory()
        print(f"数据加载后内存使用: {memory_before.percent}% ({memory_before.used/1024/1024/1024:.2f}GB)")

    # 训练数据准备
    print('准备训练数据...')
    X_temp, _, y_temp, temp_norm_params = prepare_training_data(
        temp_data, downsample_factor=args.downsample, field_type='temp',
        use_data_augmentation=args.use_data_augmentation
    )
    del temp_data
    clear_memory()

    # 详细内存监控
    if args.enable_detailed_memory:
        memory_after = psutil.virtual_memory()
        print(f"训练数据准备后内存使用: {memory_after.percent}% ({memory_after.used/1024/1024/1024:.2f}GB)")
        print(f"训练数据占用内存: {(X_temp.nbytes + y_temp.nbytes)/1024/1024/1024:.2f}GB")
    # 保存归一化参数
    temp_norm_path = os.path.join(args.model_dir, 'temp_norm_params.npy')
    if temp_norm_params:
        np.save(temp_norm_path, temp_norm_params)
        print(f'温度场归一化参数已保存到: {temp_norm_path}')
    # 构建模型
    temp_model_path = os.path.join(args.model_dir, 'temp_model.keras')
    if args.load_model and os.path.exists(temp_model_path):
        print(f'加载已有模型: {temp_model_path}')
        model = tf.keras.models.load_model(temp_model_path)
        history = None
    else:
        print('开始训练温度场模型...')
        model = create_temp_model(X_temp[0].shape, y_temp[0].shape[0], use_cosine_decay=args.use_cosine_decay)
        # 设置回调
        callbacks = []

        # 早停机制
        if args.use_early_stopping:
            early_stopping = tf.keras.callbacks.EarlyStopping(
                monitor='loss',
                patience=args.patience,
                min_delta=args.min_delta,
                verbose=1,
                restore_best_weights=True
            )
            callbacks.append(early_stopping)
            print(f"已启用早停机制: 耐心值={args.patience}, 最小改善值={args.min_delta}")

        # GPU监控
        if args.enable_gpu_monitor:
            gpu_monitor = GPUMonitor(enabled=True)
            callbacks.append(gpu_monitor)
            print("已启用GPU监控")

        # 温度场训练监控
        if args.enable_temp_monitor:
            temp_monitor = TempFieldMonitor(model_type='temp', enabled=True)
            callbacks.append(temp_monitor)
            print("已启用温度场训练监控")

        # 学习率调度器
        if args.enable_lr_scheduler:
            lr_scheduler = tf.keras.callbacks.ReduceLROnPlateau(
                monitor='loss',
                factor=args.lr_reduce_factor,
                patience=args.lr_reduce_patience,
                verbose=1,
                min_delta=args.min_delta
            )
            callbacks.append(lr_scheduler)
            print(f"已启用学习率调度器: 衰减因子={args.lr_reduce_factor}, 耐心值={args.lr_reduce_patience}")

        # 模型检查点
        if args.save_model:
            checkpoint_path = os.path.join(args.model_dir, 'temp_model_checkpoint.keras')
            checkpoint = tf.keras.callbacks.ModelCheckpoint(
                checkpoint_path,
                monitor='loss',
                save_best_only=True,
                verbose=1
            )
            callbacks.append(checkpoint)
            print(f"已启用模型检查点: {checkpoint_path}")

        # 训练模型
        print("\n开始训练温度场模型...")
        history = model.fit(
            X_temp, y_temp,
            epochs=args.epochs,
            batch_size=args.batch_size,
            callbacks=callbacks,
            verbose=1
        )
        if args.save_model:
            print(f'保存模型到: {temp_model_path}')
            model.save(temp_model_path)
            with open(os.path.join(args.results_dir, 'temp_history.pkl'), 'wb') as f:
                pickle.dump(history.history, f)
    # 预测与可视化
    print('加载少量数据用于预测...')
    pred_temp_data = load_temp_data(
        args.temp_data_dir,
        start_step=max(0, args.end_step-10), end_step=args.end_step,
        stride=1, max_samples=10
    )
    # 加载归一化参数
    temp_norm_params = None
    if os.path.exists(temp_norm_path):
        temp_norm_params = np.load(temp_norm_path, allow_pickle=True).item()
        print(f'加载温度场归一化参数: 最小值 {temp_norm_params["min"]}, 最大值 {temp_norm_params["max"]}')
    temp_pred = predict_with_original_resolution(
        model, pred_temp_data[-1], downsample_factor=args.downsample, norm_params=temp_norm_params
    )

    # 检查预测结果
    if temp_pred is not None:
        visualize_prediction(pred_temp_data[-1], temp_pred, 'temperature field', args.temp_data_dir, reshape_pred=False, save_dir=args.results_dir)
        np.save(os.path.join(args.results_dir, 'temp_prediction.npy'), temp_pred)
        compare_fields(pred_temp_data[-1], temp_pred, '温度场误差分析', args.results_dir)
        print("温度场预测和可视化完成")
    else:
        print("警告: 温度场预测失败，跳过可视化步骤")
    # 训练历史可视化
    if history is not None:
        try:
            import matplotlib.pyplot as plt
            plt.figure(figsize=(10, 6))
            plt.plot(history.history['loss'], label='Training Loss', linewidth=2, color='blue')
            if 'mae' in history.history:
                plt.plot(history.history['mae'], label='Training MAE', linewidth=2, color='red')
            plt.title('Temperature Field Training History', fontsize=14)
            plt.xlabel('Epoch', fontsize=12)
            plt.ylabel('Loss/MAE', fontsize=12)
            plt.legend(fontsize=10)
            plt.grid(True, alpha=0.3)
            plt.tight_layout()

            history_path = os.path.join(args.results_dir, 'temp_training_history.png')
            plt.savefig(history_path, dpi=300, bbox_inches='tight')
            plt.close()
            print(f"✅ 温度场训练历史图已保存到: {history_path}")
        except Exception as e:
            print(f"❌ 保存训练历史图失败: {str(e)}")
    else:
        print("⚠️ 没有训练历史数据（可能是加载了已有模型）")
    # 多步预测
    if args.generate_multi_step:
        generate_multi_step_predictions(
            model, args.temp_data_dir, 'temp',
            args.multi_step_start, args.multi_step_end, args.multi_step_interval,
            downsample_factor=args.downsample, norm_params=temp_norm_params, save_dir=args.results_dir
        )
    print('温度场训练与预测完成！')

if __name__ == '__main__':
    main()